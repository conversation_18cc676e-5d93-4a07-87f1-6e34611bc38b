// Points display in user card
.UserCard-points {
  margin-top: 5px;
  font-size: 12px;
  color: @muted-color;

  &-label {
    font-weight: normal;
  }

  &-value {
    font-weight: bold;
    color: @primary-color;
  }
}

// Points page styles
.PointsPage {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid @control-bg;
  }

  &-headerContent h1 {
    margin: 0 0 10px 0;
    color: @heading-color;
  }

  &-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
  }

  &-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: @control-bg;
    border-radius: 4px;
    min-width: 100px;
  }

  &-statLabel {
    font-size: 12px;
    color: @muted-color;
    margin-bottom: 5px;
  }

  &-statValue {
    font-size: 18px;
    font-weight: bold;
    color: @primary-color;
  }

  &-actions {
    display: flex;
    gap: 10px;
  }

  &-transactions {
    .LoadingIndicator {
      margin: 20px 0;
    }
  }

  &-transaction {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: @body-bg;
    border: 1px solid @control-bg;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      border-color: @primary-color;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    &.is-positive {
      border-left: 4px solid @success-color;
    }

    &.is-negative {
      border-left: 4px solid @danger-color;
    }
  }

  &-transactionAmount {
    font-size: 18px;
    font-weight: bold;
    margin-right: 15px;
    min-width: 60px;
    text-align: right;

    .positive {
      color: @success-color;
    }

    .negative {
      color: @danger-color;
    }
  }

  &-transactionDetails {
    flex: 1;
  }

  &-transactionType {
    font-weight: bold;
    margin-bottom: 5px;
  }

  &-transactionDescription {
    color: @muted-color;
    font-size: 14px;
    margin-bottom: 5px;
  }

  &-transactionDate {
    font-size: 12px;
    color: @muted-more-color;
  }

  &-transactionBalance {
    font-size: 14px;
    color: @muted-color;
    margin-left: 15px;
  }
}

// Points store page styles
.PointsStorePage {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid @control-bg;

    h1 {
      margin: 0;
      color: @heading-color;
    }
  }

  &-balance {
    font-size: 16px;
    color: @muted-color;
  }

  &-balanceValue {
    font-weight: bold;
    color: @primary-color;
    margin-left: 5px;
  }

  &-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  &-item {
    background: @body-bg;
    border: 1px solid @control-bg;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;

    &:hover:not(.is-disabled) {
      border-color: @primary-color;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transform: translateY(-2px);
    }

    &.is-disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &-itemHeader {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &-itemIcon {
    font-size: 24px;
    margin-right: 10px;
    color: @primary-color;
  }

  &-itemName {
    margin: 0;
    font-size: 18px;
    color: @heading-color;
  }

  &-itemDescription {
    color: @muted-color;
    margin-bottom: 15px;
    line-height: 1.4;
  }

  &-itemFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }

  &-itemCost {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  &-itemCostLabel {
    color: @muted-color;
  }

  &-itemCostValue {
    font-weight: bold;
    color: @primary-color;
    font-size: 16px;
  }

  &-itemStock {
    font-size: 14px;
  }

  &-itemStockUnlimited {
    color: @success-color;
  }

  &-itemStockValue {
    color: @muted-color;

    &.is-out-of-stock {
      color: @danger-color;
      font-weight: bold;
    }
  }
}

// Points transfer modal styles
.PointsTransferModal {
  .Form-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: @heading-color;
    }

    .helpText {
      font-size: 12px;
      color: @muted-color;
      margin-top: 5px;
    }
  }

  .FormControl {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid @control-bg;
    border-radius: 4px;
    background: @body-bg;
    color: @text-color;

    &:focus {
      border-color: @primary-color;
      outline: none;
      box-shadow: 0 0 0 2px fade(@primary-color, 20%);
    }
  }

  textarea.FormControl {
    min-height: 80px;
    resize: vertical;
  }
}

// Header points display
.Header-secondary .Button--link {
  .fa-coins {
    color: @primary-color;
  }
}

// Responsive design
@media @phone {
  .PointsPage {
    &-header {
      flex-direction: column;
      align-items: stretch;
    }

    &-stats {
      justify-content: space-around;
      margin: 15px 0;
    }

    &-actions {
      justify-content: center;
    }

    &-transaction {
      flex-direction: column;
      align-items: flex-start;
      text-align: left;
    }

    &-transactionAmount {
      margin-right: 0;
      margin-bottom: 10px;
      text-align: left;
    }

    &-transactionBalance {
      margin-left: 0;
      margin-top: 10px;
    }
  }

  .PointsStorePage {
    &-header {
      flex-direction: column;
      align-items: stretch;
      text-align: center;
    }

    &-items {
      grid-template-columns: 1fr;
    }

    &-itemFooter {
      flex-direction: column;
      align-items: stretch;
    }
  }
}

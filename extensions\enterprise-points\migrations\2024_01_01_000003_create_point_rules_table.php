<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->create('point_rules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 100); // Rule name
            $table->string('type', 50); // 'post', 'reply', 'like_received', 'like_given', 'daily_login', etc.
            $table->integer('points'); // Points awarded/deducted
            $table->boolean('is_active')->default(true);
            $table->integer('daily_limit')->nullable(); // Max times per day this rule can be applied
            $table->integer('total_limit')->nullable(); // Max times total this rule can be applied
            $table->json('conditions')->nullable(); // Additional conditions (e.g., minimum post length, specific tags)
            $table->text('description')->nullable();
            $table->integer('priority')->default(0); // Rule priority for ordering
            $table->timestamps();
            
            // Indexes
            $table->index('type');
            $table->index('is_active');
            $table->index('priority');
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->dropIfExists('point_rules');
    }
];

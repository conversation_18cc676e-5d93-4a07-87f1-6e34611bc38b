import app from 'flarum/forum/app';
import { extend } from 'flarum/common/extend';
import User from 'flarum/common/models/User';
import UserCard from 'flarum/forum/components/UserCard';
import HeaderSecondary from 'flarum/forum/components/HeaderSecondary';
import LinkButton from 'flarum/common/components/LinkButton';
import ItemList from 'flarum/common/utils/ItemList';

import PointsPage from './components/PointsPage';
import PointsStorePage from './components/PointsStorePage';
import PointsTransferModal from './components/PointsTransferModal';
import PointTransaction from './models/PointTransaction';
import PointStoreItem from './models/PointStoreItem';

app.initializers.add('enterprise-points', () => {
  // Register models
  app.store.models.pointTransactions = PointTransaction;
  app.store.models.pointStoreItems = PointStoreItem;

  // Add points attribute to User model
  User.prototype.points = function() {
    return this.attribute('points') || 0;
  };

  User.prototype.pointsRank = function() {
    return this.attribute('pointsRank');
  };

  User.prototype.totalPointsEarned = function() {
    return this.attribute('totalPointsEarned') || 0;
  };

  User.prototype.totalPointsSpent = function() {
    return this.attribute('totalPointsSpent') || 0;
  };

  // Add points display to user card
  extend(UserCard.prototype, 'infoItems', function(items) {
    const user = this.attrs.user;
    
    if (user.points() !== undefined) {
      items.add('points', 
        <div className="UserCard-points">
          <span className="UserCard-points-label">{app.translator.trans('enterprise-points.forum.user.points')}: </span>
          <span className="UserCard-points-value">{user.points()}</span>
        </div>,
        10
      );
    }
  });

  // Add points navigation to header
  extend(HeaderSecondary.prototype, 'items', function(items) {
    if (app.session.user) {
      items.add('points',
        LinkButton.component({
          href: app.route('points'),
          icon: 'fas fa-coins',
          className: 'Button Button--link'
        }, [
          app.translator.trans('enterprise-points.forum.nav.points'),
          ' (',
          app.session.user.points(),
          ')'
        ]),
        15
      );
    }
  });

  // Register routes
  app.routes.points = { path: '/points', component: PointsPage };
  app.routes.pointsStore = { path: '/points/store', component: PointsStorePage };

  // Add transfer points modal
  app.modal.show = function(componentClass, attrs = {}) {
    if (componentClass === 'PointsTransferModal') {
      return app.modal.show(PointsTransferModal, attrs);
    }
    return this.constructor.prototype.show.call(this, componentClass, attrs);
  };
});

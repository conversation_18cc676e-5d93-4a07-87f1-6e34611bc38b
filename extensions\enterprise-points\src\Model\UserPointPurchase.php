<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Model;

use Flarum\Database\AbstractModel;
use Flarum\User\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int $store_item_id
 * @property int $cost_paid
 * @property int $quantity
 * @property array|null $item_snapshot
 * @property bool $is_active
 * @property \Carbon\Carbon|null $expires_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property User $user
 * @property PointStoreItem $storeItem
 */
class UserPointPurchase extends AbstractModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_point_purchases';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'store_item_id',
        'cost_paid',
        'quantity',
        'item_snapshot',
        'is_active',
        'expires_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'item_snapshot' => 'array',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user that made the purchase.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the store item that was purchased.
     */
    public function storeItem(): BelongsTo
    {
        return $this->belongsTo(PointStoreItem::class, 'store_item_id');
    }

    /**
     * Scope a query to only include active purchases.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include non-expired purchases.
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope a query to only include expired purchases.
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->whereNotNull('expires_at')
                     ->where('expires_at', '<=', now());
    }

    /**
     * Check if the purchase is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at !== null && $this->expires_at->isPast();
    }

    /**
     * Check if the purchase is permanent (never expires).
     */
    public function isPermanent(): bool
    {
        return $this->expires_at === null;
    }

    /**
     * Get the total cost for this purchase.
     */
    public function getTotalCostAttribute(): int
    {
        return $this->cost_paid * $this->quantity;
    }

    /**
     * Get item snapshot value by key.
     */
    public function getSnapshotValue(string $key, $default = null)
    {
        return $this->item_snapshot[$key] ?? $default;
    }

    /**
     * Deactivate the purchase.
     */
    public function deactivate(): void
    {
        $this->is_active = false;
        $this->save();
    }

    /**
     * Activate the purchase.
     */
    public function activate(): void
    {
        $this->is_active = true;
        $this->save();
    }
}

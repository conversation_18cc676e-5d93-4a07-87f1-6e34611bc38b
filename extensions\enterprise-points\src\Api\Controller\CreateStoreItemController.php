<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\PointStoreItemSerializer;
use Enterprise\Points\Model\PointStoreItem;
use Flarum\Api\Controller\AbstractCreateController;
use Flarum\Http\RequestUtil;
use Flarum\User\Exception\PermissionDeniedException;
use Illuminate\Support\Arr;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for creating store items.
 */
class CreateStoreItemController extends AbstractCreateController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = PointStoreItemSerializer::class;

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $attributes = Arr::get($request->getParsedBody(), 'data.attributes', []);

        // Check permissions
        if (!$actor->hasPermission('enterprise-points.manageStore')) {
            throw new PermissionDeniedException();
        }

        // Validate required fields
        $name = Arr::get($attributes, 'name');
        $cost = (int) Arr::get($attributes, 'cost');
        $type = Arr::get($attributes, 'type', PointStoreItem::TYPE_VIRTUAL_ITEM);

        if (!$name || $cost < 0) {
            throw new \InvalidArgumentException('Missing required fields: name, cost');
        }

        // Create store item
        return PointStoreItem::create([
            'name' => $name,
            'description' => Arr::get($attributes, 'description'),
            'cost' => $cost,
            'type' => $type,
            'icon' => Arr::get($attributes, 'icon'),
            'color' => Arr::get($attributes, 'color'),
            'is_active' => (bool) Arr::get($attributes, 'isActive', true),
            'stock' => Arr::get($attributes, 'stock'),
            'purchase_limit' => Arr::get($attributes, 'purchaseLimit'),
            'metadata' => Arr::get($attributes, 'metadata', []),
            'sort_order' => (int) Arr::get($attributes, 'sortOrder', 0)
        ]);
    }
}

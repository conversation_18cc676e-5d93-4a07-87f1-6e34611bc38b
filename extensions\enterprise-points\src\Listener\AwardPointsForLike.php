<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Listener;

use Enterprise\Points\Model\PointTransaction;
use Enterprise\Points\Service\PointsService;
use Flarum\Likes\Event\PostWasLiked;
use Flarum\Likes\Event\PostWasUnliked;
use Illuminate\Contracts\Events\Dispatcher;

/**
 * Listener for awarding points when users like posts.
 */
class AwardPointsForLike
{
    /**
     * @var PointsService
     */
    protected $pointsService;

    /**
     * @param PointsService $pointsService
     */
    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * Subscribe to events.
     *
     * @param Dispatcher $events
     */
    public function subscribe(Dispatcher $events)
    {
        $events->listen(PostWasLiked::class, [$this, 'whenPostWasLiked']);
        $events->listen(PostWasUnliked::class, [$this, 'whenPostWasUnliked']);
    }

    /**
     * Handle the post liked event.
     *
     * @param PostWasLiked $event
     */
    public function whenPostWasLiked(PostWasLiked $event)
    {
        $post = $event->post;
        $liker = $event->user;
        $postAuthor = $post->user;

        // Award points to the person who liked (for giving likes)
        if ($liker && $this->pointsService->canEarnPointsToday($liker, PointTransaction::TYPE_LIKE_GIVEN)) {
            $likeGivenPoints = $this->pointsService->getPointsForRuleType(PointTransaction::TYPE_LIKE_GIVEN);
            
            if ($likeGivenPoints > 0) {
                $this->pointsService->awardPoints(
                    $liker,
                    $likeGivenPoints,
                    PointTransaction::TYPE_LIKE_GIVEN,
                    "Liked post in: {$post->discussion->title}",
                    [
                        'post_id' => $post->id,
                        'discussion_id' => $post->discussion->id,
                        'post_author_id' => $postAuthor?->id
                    ],
                    $postAuthor,
                    $post->id,
                    $post->discussion->id
                );
            }
        }

        // Award points to the post author (for receiving likes)
        if ($postAuthor && $postAuthor->id !== $liker?->id) {
            if ($this->pointsService->canEarnPointsToday($postAuthor, PointTransaction::TYPE_LIKE_RECEIVED)) {
                $likeReceivedPoints = $this->pointsService->getPointsForRuleType(PointTransaction::TYPE_LIKE_RECEIVED);
                
                if ($likeReceivedPoints > 0) {
                    $this->pointsService->awardPoints(
                        $postAuthor,
                        $likeReceivedPoints,
                        PointTransaction::TYPE_LIKE_RECEIVED,
                        "Post liked by {$liker->display_name} in: {$post->discussion->title}",
                        [
                            'post_id' => $post->id,
                            'discussion_id' => $post->discussion->id,
                            'liker_id' => $liker?->id
                        ],
                        $liker,
                        $post->id,
                        $post->discussion->id
                    );
                }
            }
        }
    }

    /**
     * Handle the post unliked event.
     *
     * @param PostWasUnliked $event
     */
    public function whenPostWasUnliked(PostWasUnliked $event)
    {
        $post = $event->post;
        $unliker = $event->user;
        $postAuthor = $post->user;

        // Note: In a real implementation, you might want to:
        // 1. Deduct points that were previously awarded
        // 2. Or simply not deduct points to avoid gaming the system
        // 3. Or implement a more complex system with point decay
        
        // For now, we'll implement a simple deduction system
        // but only if the like was given recently (within 24 hours)
        
        $recentLikeTransaction = PointTransaction::where('user_id', $unliker?->id)
            ->where('type', PointTransaction::TYPE_LIKE_GIVEN)
            ->where('related_post_id', $post->id)
            ->where('created_at', '>', now()->subDay())
            ->first();

        if ($recentLikeTransaction && $unliker) {
            try {
                $this->pointsService->spendPoints(
                    $unliker,
                    abs($recentLikeTransaction->amount),
                    'like_given_reverted',
                    "Unliked post in: {$post->discussion->title}",
                    [
                        'post_id' => $post->id,
                        'discussion_id' => $post->discussion->id,
                        'original_transaction_id' => $recentLikeTransaction->id
                    ],
                    $postAuthor,
                    $post->id,
                    $post->discussion->id
                );
            } catch (\Exception $e) {
                // If user doesn't have enough points, just log it
                // In production, you might want to use a proper logger
            }
        }

        // Similarly for the post author
        if ($postAuthor && $postAuthor->id !== $unliker?->id) {
            $recentReceivedTransaction = PointTransaction::where('user_id', $postAuthor->id)
                ->where('type', PointTransaction::TYPE_LIKE_RECEIVED)
                ->where('related_post_id', $post->id)
                ->where('related_user_id', $unliker?->id)
                ->where('created_at', '>', now()->subDay())
                ->first();

            if ($recentReceivedTransaction) {
                try {
                    $this->pointsService->spendPoints(
                        $postAuthor,
                        abs($recentReceivedTransaction->amount),
                        'like_received_reverted',
                        "Like removed by {$unliker->display_name} in: {$post->discussion->title}",
                        [
                            'post_id' => $post->id,
                            'discussion_id' => $post->discussion->id,
                            'unliker_id' => $unliker?->id,
                            'original_transaction_id' => $recentReceivedTransaction->id
                        ],
                        $unliker,
                        $post->id,
                        $post->discussion->id
                    );
                } catch (\Exception $e) {
                    // If user doesn't have enough points, just log it
                }
            }
        }
    }
}

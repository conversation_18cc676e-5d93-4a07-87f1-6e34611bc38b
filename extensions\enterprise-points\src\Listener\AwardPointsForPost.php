<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Listener;

use Enterprise\Points\Model\PointTransaction;
use Enterprise\Points\Service\PointsService;
use Flarum\Post\Event\Posted;
use Flarum\Post\Event\Saving;
use Illuminate\Contracts\Events\Dispatcher;

/**
 * Listener for awarding points when users create posts.
 */
class AwardPointsForPost
{
    /**
     * @var PointsService
     */
    protected $pointsService;

    /**
     * @param PointsService $pointsService
     */
    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * Subscribe to events.
     *
     * @param Dispatcher $events
     */
    public function subscribe(Dispatcher $events)
    {
        $events->listen(Posted::class, [$this, 'whenPostWasPosted']);
    }

    /**
     * Handle the post creation event.
     *
     * @param Posted $event
     */
    public function whenPostWasPosted(Posted $event)
    {
        $post = $event->post;
        $user = $post->user;

        // Skip if no user (shouldn't happen but safety first)
        if (!$user) {
            return;
        }

        // Determine if this is a new discussion or a reply
        $isFirstPost = $post->number === 1;
        $type = $isFirstPost ? PointTransaction::TYPE_POST : PointTransaction::TYPE_REPLY;

        // Check if user can earn points today for this action
        if (!$this->pointsService->canEarnPointsToday($user, $type)) {
            return;
        }

        // Get points amount for this action
        $points = $this->pointsService->getPointsForRuleType($type);

        if ($points > 0) {
            $description = $isFirstPost 
                ? "Created discussion: {$post->discussion->title}"
                : "Replied to discussion: {$post->discussion->title}";

            $this->pointsService->awardPoints(
                $user,
                $points,
                $type,
                $description,
                [
                    'post_id' => $post->id,
                    'discussion_id' => $post->discussion->id,
                    'post_number' => $post->number
                ],
                null,
                $post->id,
                $post->discussion->id
            );
        }
    }
}

<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->table('users', function (Blueprint $table) {
            $table->integer('points')->default(0)->after('email');
            $table->integer('total_points_earned')->default(0)->after('points');
            $table->integer('total_points_spent')->default(0)->after('total_points_earned');
            $table->timestamp('last_points_activity')->nullable()->after('total_points_spent');
            
            // Add indexes for performance
            $table->index('points');
            $table->index('total_points_earned');
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->table('users', function (Blueprint $table) {
            $table->dropColumn([
                'points',
                'total_points_earned', 
                'total_points_spent',
                'last_points_activity'
            ]);
        });
    }
];

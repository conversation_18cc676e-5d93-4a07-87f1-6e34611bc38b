{"name": "dflydev/fig-cookies", "description": "Cookies for PSR-7 HTTP Message Interface.", "license": "MIT", "keywords": ["psr7", "psr-7", "cookies"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}, "require": {"php": "^7.2 || ^8.0", "ext-pcre": "*", "psr/http-message": "^1.0.1 || ^2"}, "autoload": {"psr-4": {"Dflydev\\FigCookies\\": "src/Dflydev/FigCookies"}}, "require-dev": {"phpunit/phpunit": "^7.2.6 || ^9", "squizlabs/php_codesniffer": "^3.3", "doctrine/coding-standard": "^8", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12.16", "phpstan/extension-installer": "^1.0", "scrutinizer/ocular": "^1.8", "vimeo/psalm": "^4.4"}, "autoload-dev": {"psr-4": {"Dflydev\\FigCookies\\": "tests/Dflydev/FigCookies"}}, "extra": {"branch-alias": {"dev-main": "3.0.x-dev"}}}
<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Model;

use Flarum\Database\AbstractModel;
use Flarum\User\User;
use Flarum\Post\Post;
use Flarum\Discussion\Discussion;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int $amount
 * @property string $type
 * @property string|null $description
 * @property array|null $metadata
 * @property int|null $related_user_id
 * @property int|null $related_post_id
 * @property int|null $related_discussion_id
 * @property int $balance_after
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property User $user
 * @property User|null $relatedUser
 * @property Post|null $relatedPost
 * @property Discussion|null $relatedDiscussion
 */
class PointTransaction extends AbstractModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'point_transactions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'amount',
        'type',
        'description',
        'metadata',
        'related_user_id',
        'related_post_id',
        'related_discussion_id',
        'balance_after'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Transaction types
     */
    const TYPE_POST = 'post';
    const TYPE_REPLY = 'reply';
    const TYPE_LIKE_RECEIVED = 'like_received';
    const TYPE_LIKE_GIVEN = 'like_given';
    const TYPE_DAILY_LOGIN = 'daily_login';
    const TYPE_ADMIN_ADJUSTMENT = 'admin_adjustment';
    const TYPE_STORE_PURCHASE = 'store_purchase';
    const TYPE_TRANSFER_SENT = 'transfer_sent';
    const TYPE_TRANSFER_RECEIVED = 'transfer_received';

    /**
     * Get the user that owns the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the related user (for transfers).
     */
    public function relatedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'related_user_id');
    }

    /**
     * Get the related post.
     */
    public function relatedPost(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'related_post_id');
    }

    /**
     * Get the related discussion.
     */
    public function relatedDiscussion(): BelongsTo
    {
        return $this->belongsTo(Discussion::class, 'related_discussion_id');
    }

    /**
     * Check if this is an earning transaction.
     */
    public function isEarning(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Check if this is a spending transaction.
     */
    public function isSpending(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Get formatted amount with sign.
     */
    public function getFormattedAmountAttribute(): string
    {
        return ($this->amount > 0 ? '+' : '') . $this->amount;
    }
}

<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Model;

use Flarum\Database\AbstractModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $cost
 * @property string $type
 * @property string|null $icon
 * @property string|null $color
 * @property bool $is_active
 * @property int|null $stock
 * @property int|null $purchase_limit
 * @property array|null $metadata
 * @property int $sort_order
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PointStoreItem extends AbstractModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'point_store_items';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'cost',
        'type',
        'icon',
        'color',
        'is_active',
        'stock',
        'purchase_limit',
        'metadata',
        'sort_order'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Item types
     */
    const TYPE_BADGE = 'badge';
    const TYPE_PRIVILEGE = 'privilege';
    const TYPE_VIRTUAL_ITEM = 'virtual_item';
    const TYPE_CUSTOM = 'custom';

    /**
     * Get the purchases for this item.
     */
    public function purchases(): HasMany
    {
        return $this->hasMany(UserPointPurchase::class, 'store_item_id');
    }

    /**
     * Scope a query to only include active items.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include items of a specific type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include items in stock.
     */
    public function scopeInStock(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->whereNull('stock')
                  ->orWhere('stock', '>', 0);
        });
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeBySortOrder(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * Check if the item is in stock.
     */
    public function isInStock(): bool
    {
        return $this->stock === null || $this->stock > 0;
    }

    /**
     * Check if the item has unlimited stock.
     */
    public function hasUnlimitedStock(): bool
    {
        return $this->stock === null;
    }

    /**
     * Check if the item has a purchase limit.
     */
    public function hasPurchaseLimit(): bool
    {
        return $this->purchase_limit !== null && $this->purchase_limit > 0;
    }

    /**
     * Get the number of times this item has been purchased.
     */
    public function getTotalPurchasesAttribute(): int
    {
        return $this->purchases()->sum('quantity');
    }

    /**
     * Decrease stock by the given amount.
     */
    public function decreaseStock(int $amount = 1): void
    {
        if ($this->stock !== null) {
            $this->stock = max(0, $this->stock - $amount);
            $this->save();
        }
    }

    /**
     * Get metadata value by key.
     */
    public function getMetadata(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }
}

<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Serializer;

use Enterprise\Points\Model\UserPointPurchase;
use Flarum\Api\Serializer\AbstractSerializer;
use Flarum\Api\Serializer\BasicUserSerializer;

/**
 * Serializer for user point purchases.
 */
class UserPointPurchaseSerializer extends AbstractSerializer
{
    /**
     * {@inheritdoc}
     */
    protected $type = 'userPointPurchases';

    /**
     * {@inheritdoc}
     */
    protected function getDefaultAttributes($purchase)
    {
        /** @var UserPointPurchase $purchase */
        return [
            'id' => $purchase->id,
            'costPaid' => $purchase->cost_paid,
            'quantity' => $purchase->quantity,
            'itemSnapshot' => $purchase->item_snapshot,
            'isActive' => $purchase->is_active,
            'expiresAt' => $this->formatDate($purchase->expires_at),
            'createdAt' => $this->formatDate($purchase->created_at),
            'updatedAt' => $this->formatDate($purchase->updated_at),
            'isExpired' => $purchase->isExpired(),
            'isPermanent' => $purchase->isPermanent(),
            'totalCost' => $purchase->total_cost,
        ];
    }

    /**
     * @param UserPointPurchase $purchase
     * @return \Tobscure\JsonApi\Relationship
     */
    protected function user($purchase)
    {
        return $this->hasOne($purchase, BasicUserSerializer::class);
    }

    /**
     * @param UserPointPurchase $purchase
     * @return \Tobscure\JsonApi\Relationship
     */
    protected function storeItem($purchase)
    {
        return $this->hasOne($purchase, PointStoreItemSerializer::class);
    }
}

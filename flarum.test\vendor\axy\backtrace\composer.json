{"name": "axy/backtrace", "type": "library", "description": "Tracing in PHP", "keywords": ["Backtrace", "Trace", "Exception", "Debug"], "homepage": "https://github.com/axypro/backtrace", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.4", "phpmd/phpmd": "^2.6"}, "autoload": {"psr-4": {"axy\\backtrace\\": "src", "axy\\backtrace\\tests\\": "tests"}}, "archive": {"exclude": ["/tests/*", "/doc/*", "/cs.sh", "/phpmd.sh", "/*.xml.dist", "/CHANGELOG.md"]}}
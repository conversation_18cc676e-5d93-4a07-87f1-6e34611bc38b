<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Event;

use Flarum\User\User;

/**
 * Event fired when points are awarded to a user.
 */
class PointsAwarded
{
    /**
     * @var User
     */
    public $user;

    /**
     * @var int
     */
    public $amount;

    /**
     * @var string
     */
    public $type;

    /**
     * @var string|null
     */
    public $description;

    /**
     * @var array
     */
    public $metadata;

    /**
     * @var User|null
     */
    public $relatedUser;

    /**
     * @var int|null
     */
    public $relatedPostId;

    /**
     * @var int|null
     */
    public $relatedDiscussionId;

    /**
     * @param User $user
     * @param int $amount
     * @param string $type
     * @param string|null $description
     * @param array $metadata
     * @param User|null $relatedUser
     * @param int|null $relatedPostId
     * @param int|null $relatedDiscussionId
     */
    public function __construct(
        User $user,
        int $amount,
        string $type,
        ?string $description = null,
        array $metadata = [],
        ?User $relatedUser = null,
        ?int $relatedPostId = null,
        ?int $relatedDiscussionId = null
    ) {
        $this->user = $user;
        $this->amount = $amount;
        $this->type = $type;
        $this->description = $description;
        $this->metadata = $metadata;
        $this->relatedUser = $relatedUser;
        $this->relatedPostId = $relatedPostId;
        $this->relatedDiscussionId = $relatedDiscussionId;
    }
}

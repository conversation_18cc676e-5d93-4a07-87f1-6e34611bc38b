<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\PointTransactionSerializer;
use Enterprise\Points\Model\PointTransaction;
use <PERSON>larum\Api\Controller\AbstractListController;
use Flarum\Http\RequestUtil;
use Flarum\User\Exception\PermissionDeniedException;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for listing point transactions.
 */
class ListPointsController extends AbstractListController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = PointTransactionSerializer::class;

    /**
     * {@inheritdoc}
     */
    public $include = ['user', 'relatedUser', 'relatedPost', 'relatedDiscussion'];

    /**
     * {@inheritdoc}
     */
    public $optionalInclude = ['relatedPost.discussion', 'relatedPost.user'];

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $params = $request->getQueryParams();

        // Check permissions
        if (!$actor->hasPermission('enterprise-points.viewTransactions')) {
            throw new PermissionDeniedException();
        }

        $query = PointTransaction::query();

        // Filter by user if specified
        if (isset($params['filter']['user'])) {
            $userId = (int) $params['filter']['user'];
            
            // Users can only view their own transactions unless they have admin permission
            if ($userId !== $actor->id && !$actor->hasPermission('enterprise-points.viewAllTransactions')) {
                throw new PermissionDeniedException();
            }
            
            $query->where('user_id', $userId);
        } else {
            // If no user filter, only show actor's transactions unless admin
            if (!$actor->hasPermission('enterprise-points.viewAllTransactions')) {
                $query->where('user_id', $actor->id);
            }
        }

        // Filter by type if specified
        if (isset($params['filter']['type'])) {
            $query->where('type', $params['filter']['type']);
        }

        // Filter by date range
        if (isset($params['filter']['from'])) {
            $query->where('created_at', '>=', $params['filter']['from']);
        }

        if (isset($params['filter']['to'])) {
            $query->where('created_at', '<=', $params['filter']['to']);
        }

        // Sort by creation date (newest first)
        $query->orderBy('created_at', 'desc');

        // Apply pagination
        $limit = $this->extractLimit($request);
        $offset = $this->extractOffset($request);

        $query->skip($offset)->take($limit);

        // Load relationships
        $query->with(['user', 'relatedUser', 'relatedPost', 'relatedDiscussion']);

        return $query->get();
    }
}

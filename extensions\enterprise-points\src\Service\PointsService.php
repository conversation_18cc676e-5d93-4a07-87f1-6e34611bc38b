<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Service;

use Enterprise\Points\Event\PointsAwarded;
use Enterprise\Points\Event\PointsSpent;
use Enterprise\Points\Model\PointTransaction;
use Enterprise\Points\Model\PointRule;
use Flarum\User\User;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\ConnectionInterface;

/**
 * Service for managing user points.
 */
class PointsService
{
    /**
     * @var Dispatcher
     */
    protected $events;

    /**
     * @var ConnectionInterface
     */
    protected $db;

    /**
     * @param Dispatcher $events
     * @param ConnectionInterface $db
     */
    public function __construct(Dispatcher $events, ConnectionInterface $db)
    {
        $this->events = $events;
        $this->db = $db;
    }

    /**
     * Award points to a user.
     *
     * @param User $user
     * @param int $amount
     * @param string $type
     * @param string|null $description
     * @param array $metadata
     * @param User|null $relatedUser
     * @param int|null $relatedPostId
     * @param int|null $relatedDiscussionId
     * @return PointTransaction
     */
    public function awardPoints(
        User $user,
        int $amount,
        string $type,
        ?string $description = null,
        array $metadata = [],
        ?User $relatedUser = null,
        ?int $relatedPostId = null,
        ?int $relatedDiscussionId = null
    ): PointTransaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Amount must be positive for awarding points');
        }

        return $this->db->transaction(function () use (
            $user, $amount, $type, $description, $metadata, 
            $relatedUser, $relatedPostId, $relatedDiscussionId
        ) {
            // Update user points
            $user->points = ($user->points ?? 0) + $amount;
            $user->total_points_earned = ($user->total_points_earned ?? 0) + $amount;
            $user->last_points_activity = now();
            $user->save();

            // Create transaction record
            $transaction = PointTransaction::create([
                'user_id' => $user->id,
                'amount' => $amount,
                'type' => $type,
                'description' => $description,
                'metadata' => $metadata,
                'related_user_id' => $relatedUser?->id,
                'related_post_id' => $relatedPostId,
                'related_discussion_id' => $relatedDiscussionId,
                'balance_after' => $user->points
            ]);

            // Fire event
            $this->events->dispatch(new PointsAwarded(
                $user, $amount, $type, $description, $metadata,
                $relatedUser, $relatedPostId, $relatedDiscussionId
            ));

            return $transaction;
        });
    }

    /**
     * Spend points from a user.
     *
     * @param User $user
     * @param int $amount
     * @param string $type
     * @param string|null $description
     * @param array $metadata
     * @param User|null $relatedUser
     * @param int|null $relatedPostId
     * @param int|null $relatedDiscussionId
     * @return PointTransaction
     * @throws \Exception
     */
    public function spendPoints(
        User $user,
        int $amount,
        string $type,
        ?string $description = null,
        array $metadata = [],
        ?User $relatedUser = null,
        ?int $relatedPostId = null,
        ?int $relatedDiscussionId = null
    ): PointTransaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Amount must be positive for spending points');
        }

        if (($user->points ?? 0) < $amount) {
            throw new \Exception('Insufficient points');
        }

        return $this->db->transaction(function () use (
            $user, $amount, $type, $description, $metadata,
            $relatedUser, $relatedPostId, $relatedDiscussionId
        ) {
            // Update user points
            $user->points = ($user->points ?? 0) - $amount;
            $user->total_points_spent = ($user->total_points_spent ?? 0) + $amount;
            $user->last_points_activity = now();
            $user->save();

            // Create transaction record (negative amount)
            $transaction = PointTransaction::create([
                'user_id' => $user->id,
                'amount' => -$amount,
                'type' => $type,
                'description' => $description,
                'metadata' => $metadata,
                'related_user_id' => $relatedUser?->id,
                'related_post_id' => $relatedPostId,
                'related_discussion_id' => $relatedDiscussionId,
                'balance_after' => $user->points
            ]);

            // Fire event
            $this->events->dispatch(new PointsSpent(
                $user, $amount, $type, $description, $metadata,
                $relatedUser, $relatedPostId, $relatedDiscussionId
            ));

            return $transaction;
        });
    }

    /**
     * Get points for a specific rule type.
     *
     * @param string $type
     * @return int
     */
    public function getPointsForRuleType(string $type): int
    {
        $rule = PointRule::active()->ofType($type)->byPriority()->first();
        
        return $rule ? $rule->points : 0;
    }

    /**
     * Check if user can earn points for a specific rule today.
     *
     * @param User $user
     * @param string $type
     * @return bool
     */
    public function canEarnPointsToday(User $user, string $type): bool
    {
        $rule = PointRule::active()->ofType($type)->byPriority()->first();
        
        if (!$rule || !$rule->hasDailyLimit()) {
            return true;
        }

        $todayCount = PointTransaction::where('user_id', $user->id)
            ->where('type', $type)
            ->where('amount', '>', 0)
            ->whereDate('created_at', today())
            ->count();

        return $todayCount < $rule->daily_limit;
    }

    /**
     * Transfer points between users.
     *
     * @param User $sender
     * @param User $recipient
     * @param int $amount
     * @param string|null $message
     * @return array [PointTransaction, PointTransaction]
     * @throws \Exception
     */
    public function transferPoints(User $sender, User $recipient, int $amount, ?string $message = null): array
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Amount must be positive');
        }

        if ($sender->id === $recipient->id) {
            throw new \InvalidArgumentException('Cannot transfer points to yourself');
        }

        return $this->db->transaction(function () use ($sender, $recipient, $amount, $message) {
            // Spend points from sender
            $spendTransaction = $this->spendPoints(
                $sender,
                $amount,
                PointTransaction::TYPE_TRANSFER_SENT,
                $message ? "Transfer to {$recipient->display_name}: {$message}" : "Transfer to {$recipient->display_name}",
                ['recipient_id' => $recipient->id, 'message' => $message],
                $recipient
            );

            // Award points to recipient
            $awardTransaction = $this->awardPoints(
                $recipient,
                $amount,
                PointTransaction::TYPE_TRANSFER_RECEIVED,
                $message ? "Transfer from {$sender->display_name}: {$message}" : "Transfer from {$sender->display_name}",
                ['sender_id' => $sender->id, 'message' => $message],
                $sender
            );

            return [$spendTransaction, $awardTransaction];
        });
    }
}

import Model from 'flarum/common/Model';

export default class PointStoreItem extends Model {
  name = Model.attribute('name');
  description = Model.attribute('description');
  cost = Model.attribute('cost');
  type = Model.attribute('type');
  icon = Model.attribute('icon');
  color = Model.attribute('color');
  isActive = Model.attribute('isActive');
  stock = Model.attribute('stock');
  purchaseLimit = Model.attribute('purchaseLimit');
  metadata = Model.attribute('metadata');
  sortOrder = Model.attribute('sortOrder');
  createdAt = Model.attribute('createdAt', Model.transformDate);
  updatedAt = Model.attribute('updatedAt', Model.transformDate);
  isInStock = Model.attribute('isInStock');
  hasUnlimitedStock = Model.attribute('hasUnlimitedStock');
  hasPurchaseLimit = Model.attribute('hasPurchaseLimit');
  totalPurchases = Model.attribute('totalPurchases');
}

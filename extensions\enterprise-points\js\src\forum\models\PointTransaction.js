import Model from 'flarum/common/Model';

export default class PointTransaction extends Model {
  amount = Model.attribute('amount');
  type = Model.attribute('type');
  description = Model.attribute('description');
  metadata = Model.attribute('metadata');
  balanceAfter = Model.attribute('balanceAfter');
  createdAt = Model.attribute('createdAt', Model.transformDate);
  updatedAt = Model.attribute('updatedAt', Model.transformDate);
  formattedAmount = Model.attribute('formattedAmount');
  isEarning = Model.attribute('isEarning');
  isSpending = Model.attribute('isSpending');

  user = Model.hasOne('user');
  relatedUser = Model.hasOne('relatedUser');
  relatedPost = Model.hasOne('relatedPost');
  relatedDiscussion = Model.hasOne('relatedDiscussion');
}

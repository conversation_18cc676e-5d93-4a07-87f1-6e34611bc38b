import app from 'flarum/forum/app';
import Modal from 'flarum/common/components/Modal';
import Button from 'flarum/common/components/Button';
import Stream from 'flarum/common/utils/Stream';

export default class PointsTransferModal extends Modal {
  oninit(vnode) {
    super.oninit(vnode);

    this.recipient = Stream('');
    this.amount = Stream('');
    this.message = Stream('');
    this.loading = false;
  }

  className() {
    return 'PointsTransferModal Modal--small';
  }

  title() {
    return app.translator.trans('enterprise-points.forum.transfer.title');
  }

  content() {
    return (
      <div className="Modal-body">
        <div className="Form">
          <div className="Form-group">
            <label>{app.translator.trans('enterprise-points.forum.transfer.recipient')}</label>
            <input
              className="FormControl"
              type="text"
              placeholder={app.translator.trans('enterprise-points.forum.transfer.recipient')}
              bidi={this.recipient}
            />
          </div>

          <div className="Form-group">
            <label>{app.translator.trans('enterprise-points.forum.transfer.amount')}</label>
            <input
              className="FormControl"
              type="number"
              min="1"
              max={app.session.user.points()}
              placeholder="0"
              bidi={this.amount}
            />
            <div className="helpText">
              {app.translator.trans('enterprise-points.forum.points.your_points')}: {app.session.user.points()}
            </div>
          </div>

          <div className="Form-group">
            <label>{app.translator.trans('enterprise-points.forum.transfer.message')}</label>
            <textarea
              className="FormControl"
              placeholder={app.translator.trans('enterprise-points.forum.transfer.message')}
              bidi={this.message}
            />
          </div>

          <div className="Form-group">
            <Button
              className="Button Button--primary Button--block"
              type="submit"
              loading={this.loading}
              disabled={!this.recipient() || !this.amount() || this.amount() <= 0}
            >
              {app.translator.trans('enterprise-points.forum.transfer.send')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  onsubmit(e) {
    e.preventDefault();

    if (this.loading) return;

    const amount = parseInt(this.amount());
    const userPoints = app.session.user.points();

    if (amount <= 0) {
      app.alerts.show({ type: 'error' }, app.translator.trans('enterprise-points.forum.transfer.invalid_amount'));
      return;
    }

    if (amount > userPoints) {
      app.alerts.show({ type: 'error' }, app.translator.trans('enterprise-points.forum.store.insufficient_points'));
      return;
    }

    this.loading = true;

    // First, find the recipient user
    app.store.find('users', { filter: { q: this.recipient() } })
      .then(users => {
        const recipient = users.find(user => 
          user.username().toLowerCase() === this.recipient().toLowerCase() ||
          user.displayName().toLowerCase() === this.recipient().toLowerCase()
        );

        if (!recipient) {
          throw new Error('User not found');
        }

        // Create the transfer transaction
        return app.store.createRecord('pointTransactions').save({
          type: 'transfer',
          userId: app.session.user.id(),
          recipientId: recipient.id(),
          amount: amount,
          description: this.message()
        });
      })
      .then(() => {
        // Update user points
        app.session.user.pushData({
          attributes: {
            points: userPoints - amount
          }
        });

        app.alerts.show({ type: 'success' }, app.translator.trans('enterprise-points.forum.transfer.success'));
        app.modal.close();
      })
      .catch(error => {
        console.error('Transfer error:', error);
        app.alerts.show({ type: 'error' }, app.translator.trans('enterprise-points.forum.transfer.error'));
      })
      .finally(() => {
        this.loading = false;
        m.redraw();
      });
  }
}

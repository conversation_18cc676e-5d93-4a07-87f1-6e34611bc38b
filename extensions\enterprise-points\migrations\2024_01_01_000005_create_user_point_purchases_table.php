<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->create('user_point_purchases', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('store_item_id');
            $table->integer('cost_paid'); // Points spent at time of purchase
            $table->integer('quantity')->default(1);
            $table->json('item_snapshot')->nullable(); // Store item data at time of purchase
            $table->boolean('is_active')->default(true); // Can be deactivated by admin
            $table->timestamp('expires_at')->nullable(); // For time-limited items
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('store_item_id')->references('id')->on('point_store_items')->onDelete('cascade');
            
            // Indexes
            $table->index('user_id');
            $table->index('store_item_id');
            $table->index('is_active');
            $table->index('expires_at');
            $table->index(['user_id', 'store_item_id']);
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->dropIfExists('user_point_purchases');
    }
];

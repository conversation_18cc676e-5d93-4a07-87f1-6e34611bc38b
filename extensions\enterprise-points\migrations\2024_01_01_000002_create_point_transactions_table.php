<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->create('point_transactions', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->integer('amount'); // Positive for earning, negative for spending
            $table->string('type', 50); // 'post', 'reply', 'like_received', 'like_given', 'daily_login', 'admin_adjustment', 'store_purchase', 'transfer_sent', 'transfer_received'
            $table->string('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data like post_id, recipient_id, etc.
            $table->unsignedInteger('related_user_id')->nullable(); // For transfers
            $table->unsignedInteger('related_post_id')->nullable(); // For post-related transactions
            $table->unsignedInteger('related_discussion_id')->nullable(); // For discussion-related transactions
            $table->integer('balance_after'); // User's balance after this transaction
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('related_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('related_post_id')->references('id')->on('posts')->onDelete('set null');
            $table->foreign('related_discussion_id')->references('id')->on('discussions')->onDelete('set null');
            
            // Indexes for performance
            $table->index('user_id');
            $table->index('type');
            $table->index('created_at');
            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'created_at']);
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->dropIfExists('point_transactions');
    }
];

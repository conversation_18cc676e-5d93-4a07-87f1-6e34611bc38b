{"name": "carbonphp/carbon-doctrine-types", "description": "Types to use Carbon in Doctrine", "type": "library", "keywords": ["date", "time", "DateTime", "Carbon", "Doctrine"], "require": {"php": "^7.1.8 || ^8.0"}, "require-dev": {"doctrine/dbal": ">=2.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "conflict": {"doctrine/dbal": ">=3.7.0"}, "license": "MIT", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "minimum-stability": "dev"}
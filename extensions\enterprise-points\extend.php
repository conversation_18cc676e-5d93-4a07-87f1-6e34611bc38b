<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points;

use Flarum\Api\Controller;
use Flarum\Api\Serializer\UserSerializer;
use Flarum\Extend;
use Flarum\User\User;
use Enterprise\Points\Api\Controller\ListPointsController;
use Enterprise\Points\Api\Controller\CreatePointTransactionController;
use Enterprise\Points\Api\Controller\ShowPointsStoreController;
use Enterprise\Points\Api\Serializer\PointTransactionSerializer;
use Enterprise\Points\Event\PointsAwarded;
use Enterprise\Points\Event\PointsSpent;
use Enterprise\Points\Listener\AwardPointsForPost;
use Enterprise\Points\Listener\AwardPointsForLike;
use Enterprise\Points\Listener\SavePointsToDatabase;
use Enterprise\Points\Service\PointsService;

return [
    // Frontend assets
    (new Extend\Frontend('forum'))
        ->js(__DIR__.'/js/dist/forum.js')
        ->css(__DIR__.'/less/forum.less'),

    (new Extend\Frontend('admin'))
        ->js(__DIR__.'/js/dist/admin.js')
        ->css(__DIR__.'/less/admin.less'),

    // Localization
    new Extend\Locales(__DIR__.'/locale'),

    // Database migrations
    (new Extend\Migration())
        ->add(__DIR__.'/migrations'),

    // User model extensions
    (new Extend\Model(User::class))
        ->hasMany('pointTransactions', 'Enterprise\Points\Model\PointTransaction', 'user_id'),

    // API serializer extensions
    (new Extend\ApiSerializer(UserSerializer::class))
        ->attribute('points', function (UserSerializer $serializer, User $user) {
            return $user->points ?? 0;
        })
        ->attribute('totalPointsEarned', function (UserSerializer $serializer, User $user) {
            return $user->total_points_earned ?? 0;
        })
        ->attribute('totalPointsSpent', function (UserSerializer $serializer, User $user) {
            return $user->total_points_spent ?? 0;
        })
        ->attribute('pointsRank', function (UserSerializer $serializer, User $user) {
            // Calculate user's rank based on points
            return User::where('points', '>', $user->points ?? 0)->count() + 1;
        }),

    // API routes
    (new Extend\Routes('api'))
        ->get('/points', 'points.index', ListPointsController::class)
        ->post('/points/transaction', 'points.transaction', CreatePointTransactionController::class)
        ->get('/points/store', 'points.store', ShowPointsStoreController::class),

    // Event listeners
    (new Extend\Event())
        ->listen(PointsAwarded::class, SavePointsToDatabase::class)
        ->listen(PointsSpent::class, [SavePointsToDatabase::class, 'handleSpent'])
        ->subscribe(AwardPointsForPost::class)
        ->subscribe(AwardPointsForLike::class),

    // Service provider
    (new Extend\ServiceProvider())
        ->register(function ($container) {
            $container->singleton(PointsService::class, function ($container) {
                return new PointsService(
                    $container->make('events'),
                    $container->make('flarum.db.connection')
                );
            });
        }),

    // Settings
    (new Extend\Settings())
        ->default('enterprise-points.points_per_post', 10)
        ->default('enterprise-points.points_per_reply', 5)
        ->default('enterprise-points.points_per_like_received', 2)
        ->default('enterprise-points.points_per_like_given', 1)
        ->default('enterprise-points.daily_login_points', 5)
        ->default('enterprise-points.enable_point_store', true)
        ->default('enterprise-points.enable_point_transfer', true)
        ->default('enterprise-points.min_transfer_amount', 1)
        ->default('enterprise-points.max_transfer_amount', 1000),
];

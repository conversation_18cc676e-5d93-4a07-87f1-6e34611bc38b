<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->create('point_store_items', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 200);
            $table->text('description')->nullable();
            $table->integer('cost'); // Points required to purchase
            $table->string('type', 50); // 'badge', 'privilege', 'virtual_item', 'custom'
            $table->string('icon', 100)->nullable(); // Icon class or image URL
            $table->string('color', 7)->nullable(); // Hex color code
            $table->boolean('is_active')->default(true);
            $table->integer('stock')->nullable(); // NULL for unlimited, 0 for out of stock
            $table->integer('purchase_limit')->nullable(); // Max purchases per user
            $table->json('metadata')->nullable(); // Additional item data
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index('type');
            $table->index('is_active');
            $table->index('sort_order');
            $table->index('cost');
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->dropIfExists('point_store_items');
    }
];

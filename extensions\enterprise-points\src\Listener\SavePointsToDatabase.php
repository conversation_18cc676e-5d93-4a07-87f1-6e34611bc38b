<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Listener;

use Enterprise\Points\Event\PointsAwarded;
use Enterprise\Points\Event\PointsSpent;

/**
 * Listener for saving points transactions to database.
 * Note: The actual database saving is handled in the PointsService,
 * this listener can be used for additional processing like notifications.
 */
class SavePointsToDatabase
{
    /**
     * Handle points awarded event.
     *
     * @param PointsAwarded $event
     */
    public function handle(PointsAwarded $event)
    {
        // The actual saving is done in PointsService
        // This can be used for additional processing like:
        // - Sending notifications
        // - Updating statistics
        // - Triggering achievements
        // - Logging for analytics
        
        // For now, we'll just ensure the user model is refreshed
        $event->user->refresh();
        
        // You could add notification logic here
        // Example:
        // if ($event->amount >= 100) {
        //     // Send notification for large point awards
        // }
    }

    /**
     * Handle points spent event.
     *
     * @param PointsSpent $event
     */
    public function handleSpent(PointsSpent $event)
    {
        // Similar to above, additional processing for spent points
        $event->user->refresh();
        
        // You could add logic here for:
        // - Tracking spending patterns
        // - Sending confirmations
        // - Updating user badges/achievements
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
        <defaultDocument>
            <files>
                <clear />
                <add value="Index.php" />
            </files>
        </defaultDocument>
        <rewrite>
            <rules>
                <!-- Changed `enabled=` to true in the rule below if you are not using the `public` directory to
                prevent sensitive resources from being exposed -->
                <rule name="Disallow sensitive directories" enabled="false" stopProcessing="true">
                    <match url="^/(\.git|composer\.(json|lock)|auth\.json|config\.php|flarum|storage|vendor)" ignoreCase="false" />
                    <action type="CustomResponse" url="/" statusCode="403" statusReason="Forbidden" statusDescription="Forbidden" />
                </rule>
                <rule name="Handle index.php re-write" stopProcessing="true">
                    <match url="^" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>
  </system.webServer>
</configuration>

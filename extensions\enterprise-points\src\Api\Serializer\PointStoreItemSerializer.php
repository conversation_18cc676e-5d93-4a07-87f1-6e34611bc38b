<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Serializer;

use Enterprise\Points\Model\PointStoreItem;
use Flarum\Api\Serializer\AbstractSerializer;

/**
 * Serializer for point store items.
 */
class PointStoreItemSerializer extends AbstractSerializer
{
    /**
     * {@inheritdoc}
     */
    protected $type = 'pointStoreItems';

    /**
     * {@inheritdoc}
     */
    protected function getDefaultAttributes($item)
    {
        /** @var PointStoreItem $item */
        return [
            'id' => $item->id,
            'name' => $item->name,
            'description' => $item->description,
            'cost' => $item->cost,
            'type' => $item->type,
            'icon' => $item->icon,
            'color' => $item->color,
            'isActive' => $item->is_active,
            'stock' => $item->stock,
            'purchaseLimit' => $item->purchase_limit,
            'metadata' => $item->metadata,
            'sortOrder' => $item->sort_order,
            'createdAt' => $this->formatDate($item->created_at),
            'updatedAt' => $this->formatDate($item->updated_at),
            'isInStock' => $item->isInStock(),
            'hasUnlimitedStock' => $item->hasUnlimitedStock(),
            'hasPurchaseLimit' => $item->hasPurchaseLimit(),
            'totalPurchases' => $item->total_purchases,
        ];
    }
}

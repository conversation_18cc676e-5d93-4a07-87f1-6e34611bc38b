import app from 'flarum/forum/app';
import Page from 'flarum/common/components/Page';
import Button from 'flarum/common/components/Button';
import LoadingIndicator from 'flarum/common/components/LoadingIndicator';
import Placeholder from 'flarum/common/components/Placeholder';

export default class PointsStorePage extends Page {
  oninit(vnode) {
    super.oninit(vnode);

    this.loading = true;
    this.items = [];

    this.loadStoreItems();
  }

  view() {
    return (
      <div className="PointsStorePage">
        <div className="container">
          <div className="PointsStorePage-header">
            <h1>{app.translator.trans('enterprise-points.forum.store.title')}</h1>
            <div className="PointsStorePage-balance">
              {app.translator.trans('enterprise-points.forum.points.your_points')}: 
              <span className="PointsStorePage-balanceValue">{app.session.user.points()}</span>
            </div>
          </div>

          <div className="PointsStorePage-content">
            {this.loading ? (
              <LoadingIndicator />
            ) : this.items.length ? (
              <div className="PointsStorePage-items">
                {this.items.map(item => this.storeItem(item))}
              </div>
            ) : (
              <Placeholder text={app.translator.trans('enterprise-points.forum.store.no_items')} />
            )}
          </div>
        </div>
      </div>
    );
  }

  storeItem(item) {
    const userPoints = app.session.user.points();
    const canAfford = userPoints >= item.cost();
    const inStock = item.isInStock();
    const canPurchase = canAfford && inStock;

    return (
      <div className={`PointsStorePage-item ${!canPurchase ? 'is-disabled' : ''}`}>
        <div className="PointsStorePage-itemHeader">
          {item.icon() && (
            <i 
              className={`PointsStorePage-itemIcon ${item.icon()}`}
              style={item.color() ? { color: item.color() } : {}}
            />
          )}
          <h3 className="PointsStorePage-itemName">{item.name()}</h3>
        </div>

        {item.description() && (
          <div className="PointsStorePage-itemDescription">
            {item.description()}
          </div>
        )}

        <div className="PointsStorePage-itemFooter">
          <div className="PointsStorePage-itemCost">
            <span className="PointsStorePage-itemCostLabel">
              {app.translator.trans('enterprise-points.forum.store.cost')}:
            </span>
            <span className="PointsStorePage-itemCostValue">
              {item.cost()}
            </span>
          </div>

          <div className="PointsStorePage-itemStock">
            {item.hasUnlimitedStock() ? (
              <span className="PointsStorePage-itemStockUnlimited">
                {app.translator.trans('enterprise-points.forum.store.unlimited')}
              </span>
            ) : (
              <span className={`PointsStorePage-itemStockValue ${!inStock ? 'is-out-of-stock' : ''}`}>
                {inStock ? 
                  `${app.translator.trans('enterprise-points.forum.store.stock')}: ${item.stock()}` :
                  app.translator.trans('enterprise-points.forum.store.out_of_stock')
                }
              </span>
            )}
          </div>

          <Button
            className={`Button ${canPurchase ? 'Button--primary' : 'Button--disabled'}`}
            disabled={!canPurchase}
            onclick={() => this.purchaseItem(item)}
          >
            {!canAfford ? 
              app.translator.trans('enterprise-points.forum.store.insufficient_points') :
              !inStock ?
                app.translator.trans('enterprise-points.forum.store.out_of_stock') :
                app.translator.trans('enterprise-points.forum.store.purchase')
            }
          </Button>
        </div>
      </div>
    );
  }

  purchaseItem(item) {
    if (confirm(app.translator.trans('enterprise-points.forum.store.confirm_purchase', { 
      item: item.name(), 
      cost: item.cost() 
    }))) {
      app.store.createRecord('userPointPurchases').save({
        storeItemId: item.id(),
        quantity: 1
      }).then(() => {
        // Update user points
        const newPoints = app.session.user.points() - item.cost();
        app.session.user.pushData({
          attributes: { points: newPoints }
        });

        // Update item stock if not unlimited
        if (!item.hasUnlimitedStock()) {
          item.pushData({
            attributes: { stock: item.stock() - 1 }
          });
        }

        app.alerts.show({ type: 'success' }, app.translator.trans('enterprise-points.forum.store.purchase_success'));
        m.redraw();
      }).catch(error => {
        console.error('Purchase error:', error);
        app.alerts.show({ type: 'error' }, app.translator.trans('enterprise-points.forum.store.purchase_error'));
      });
    }
  }

  loadStoreItems() {
    return app.store.find('pointStoreItems')
      .then(items => {
        this.items = items;
        this.loading = false;
        m.redraw();
      });
  }
}

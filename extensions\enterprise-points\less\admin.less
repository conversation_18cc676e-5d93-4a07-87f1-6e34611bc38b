.PointsSettingsPage {
  &-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: @primary-color;
    color: white;
    border-radius: 8px;
  }

  &-headerIcon {
    font-size: 48px;
    margin-right: 20px;
    opacity: 0.8;
  }

  &-headerText {
    h2 {
      margin: 0 0 5px 0;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      opacity: 0.9;
      font-size: 14px;
    }
  }

  &-content {
    .Section {
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 20px;
      overflow: hidden;

      &-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e8e8e8;

        h3 {
          margin: 0 0 5px 0;
          font-size: 18px;
          color: #333;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }

      &-body {
        padding: 20px;

        .Form-group {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
          }

          .FormControl {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;

            &:focus {
              border-color: @primary-color;
              outline: none;
              box-shadow: 0 0 0 2px fade(@primary-color, 20%);
            }
          }

          .Switch {
            margin-bottom: 0;
          }

          .Button {
            margin-right: 10px;

            i {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

// Admin navigation enhancement
.AdminNav-index .AdminNav-link[href*="enterprise-points"] {
  .icon {
    color: #f39c12;
  }
}

// Form styling improvements
.Form-group {
  .helpText {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
  }

  .Switch-label {
    font-weight: 600;
    color: #333;
  }
}

// Responsive design
@media (max-width: 768px) {
  .PointsSettingsPage {
    &-header {
      flex-direction: column;
      text-align: center;
    }

    &-headerIcon {
      margin-right: 0;
      margin-bottom: 15px;
    }

    &-content {
      .Section-body {
        padding: 15px;

        .Form-group {
          .FormControl {
            max-width: 100%;
          }

          .Button {
            width: 100%;
            margin-right: 0;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}

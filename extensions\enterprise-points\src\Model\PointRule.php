<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Model;

use Flarum\Database\AbstractModel;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $id
 * @property string $name
 * @property string $type
 * @property int $points
 * @property bool $is_active
 * @property int|null $daily_limit
 * @property int|null $total_limit
 * @property array|null $conditions
 * @property string|null $description
 * @property int $priority
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PointRule extends AbstractModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'point_rules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'type',
        'points',
        'is_active',
        'daily_limit',
        'total_limit',
        'conditions',
        'description',
        'priority'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'conditions' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Rule types
     */
    const TYPE_POST = 'post';
    const TYPE_REPLY = 'reply';
    const TYPE_LIKE_RECEIVED = 'like_received';
    const TYPE_LIKE_GIVEN = 'like_given';
    const TYPE_DAILY_LOGIN = 'daily_login';
    const TYPE_REGISTRATION = 'registration';
    const TYPE_PROFILE_COMPLETE = 'profile_complete';

    /**
     * Scope a query to only include active rules.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include rules of a specific type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to order by priority.
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc')->orderBy('id');
    }

    /**
     * Check if the rule has conditions.
     */
    public function hasConditions(): bool
    {
        return !empty($this->conditions);
    }

    /**
     * Get a specific condition value.
     */
    public function getCondition(string $key, $default = null)
    {
        return $this->conditions[$key] ?? $default;
    }

    /**
     * Check if the rule has a daily limit.
     */
    public function hasDailyLimit(): bool
    {
        return $this->daily_limit !== null && $this->daily_limit > 0;
    }

    /**
     * Check if the rule has a total limit.
     */
    public function hasTotalLimit(): bool
    {
        return $this->total_limit !== null && $this->total_limit > 0;
    }
}

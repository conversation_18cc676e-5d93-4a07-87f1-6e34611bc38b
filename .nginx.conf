# Pass requests that don't refer directly to files in the filesystem to index.php
location / {
  try_files $uri $uri/ /index.php?$query_string;
}

# Uncomment the following lines if you are not using a `public` directory
# to prevent sensitive resources from being exposed.
# <!-- BEGIN EXPOSED RESOURCES PROTECTION -->
# location ~* ^/(\.git|composer\.(json|lock)|auth\.json|config\.php|flarum|storage|vendor) {
#   deny all;
#   return 404;
# }
# <!-- END EXPOSED RESOURCES PROTECTION -->

# The following directives are based on best practices from H5BP Nginx Server Configs
# https://github.com/h5bp/server-configs-nginx

# Expire rules for static content
location ~* \.(?:manifest|appcache|html?|xml|json)$ {
  add_header Cache-Control "max-age=0";
}

location ~* \.(?:rss|atom)$ {
  add_header Cache-Control "max-age=3600";
}

location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|mp4|ogg|ogv|webm|htc)$ {
  add_header Cache-Control "max-age=2592000";
  access_log off;
}

location ~* \.(?:css|js)$ {
  add_header Cache-Control "max-age=31536000";
  access_log off;
}

location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
  add_header Cache-Control "max-age=2592000";
  access_log off;
}

# Gzip compression
gzip on;
gzip_comp_level 5;
gzip_min_length 256;
gzip_proxied any;
gzip_vary on;
gzip_types
    application/atom+xml
    application/javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rss+xml
    application/vnd.geo+json
    application/vnd.ms-fontobject
    application/x-font-ttf
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/opentype
    image/bmp
    image/svg+xml
    image/x-icon
    text/cache-manifest
    text/css
    text/javascript
    text/plain
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy;

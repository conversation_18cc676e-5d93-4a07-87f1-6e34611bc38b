import app from 'flarum/forum/app';
import Page from 'flarum/common/components/Page';
import Button from 'flarum/common/components/Button';
import LoadingIndicator from 'flarum/common/components/LoadingIndicator';
import Placeholder from 'flarum/common/components/Placeholder';
import ItemList from 'flarum/common/utils/ItemList';
import listItems from 'flarum/common/helpers/listItems';

import PointsTransferModal from './PointsTransferModal';

export default class PointsPage extends Page {
  oninit(vnode) {
    super.oninit(vnode);

    this.loading = true;
    this.transactions = [];
    this.moreResults = false;

    this.loadTransactions();
  }

  view() {
    return (
      <div className="PointsPage">
        <div className="container">
          <div className="PointsPage-header">
            <div className="PointsPage-headerContent">
              <h1>{app.translator.trans('enterprise-points.forum.points.title')}</h1>
              <div className="PointsPage-stats">
                <div className="PointsPage-stat">
                  <span className="PointsPage-statLabel">
                    {app.translator.trans('enterprise-points.forum.points.your_points')}
                  </span>
                  <span className="PointsPage-statValue">
                    {app.session.user.points()}
                  </span>
                </div>
                <div className="PointsPage-stat">
                  <span className="PointsPage-statLabel">
                    {app.translator.trans('enterprise-points.forum.user.total_earned')}
                  </span>
                  <span className="PointsPage-statValue">
                    {app.session.user.totalPointsEarned()}
                  </span>
                </div>
                <div className="PointsPage-stat">
                  <span className="PointsPage-statLabel">
                    {app.translator.trans('enterprise-points.forum.user.total_spent')}
                  </span>
                  <span className="PointsPage-statValue">
                    {app.session.user.totalPointsSpent()}
                  </span>
                </div>
              </div>
            </div>
            <div className="PointsPage-actions">
              {this.actionItems().toArray()}
            </div>
          </div>

          <div className="PointsPage-content">
            <h3>{app.translator.trans('enterprise-points.forum.points.recent_transactions')}</h3>
            
            {this.loading ? (
              <LoadingIndicator />
            ) : this.transactions.length ? (
              <div className="PointsPage-transactions">
                {this.transactions.map(transaction => this.transactionItem(transaction))}
                {this.moreResults && (
                  <Button 
                    className="Button Button--block"
                    onclick={() => this.loadMore()}
                  >
                    {app.translator.trans('core.forum.discussion_list.load_more_button')}
                  </Button>
                )}
              </div>
            ) : (
              <Placeholder text={app.translator.trans('enterprise-points.forum.transactions.no_transactions')} />
            )}
          </div>
        </div>
      </div>
    );
  }

  actionItems() {
    const items = new ItemList();

    if (app.forum.attribute('enterprise-points.enable_point_transfer')) {
      items.add('transfer',
        Button.component({
          className: 'Button Button--primary',
          icon: 'fas fa-exchange-alt',
          onclick: () => app.modal.show(PointsTransferModal)
        }, app.translator.trans('enterprise-points.forum.transfer.title'))
      );
    }

    if (app.forum.attribute('enterprise-points.enable_point_store')) {
      items.add('store',
        Button.component({
          className: 'Button',
          icon: 'fas fa-shopping-cart',
          onclick: () => m.route.set(app.route('pointsStore'))
        }, app.translator.trans('enterprise-points.forum.nav.store'))
      );
    }

    return items;
  }

  transactionItem(transaction) {
    const amount = transaction.amount();
    const isPositive = amount > 0;
    
    return (
      <div className={`PointsPage-transaction ${isPositive ? 'is-positive' : 'is-negative'}`}>
        <div className="PointsPage-transactionAmount">
          <span className={`PointsPage-transactionSign ${isPositive ? 'positive' : 'negative'}`}>
            {isPositive ? '+' : ''}
          </span>
          {Math.abs(amount)}
        </div>
        <div className="PointsPage-transactionDetails">
          <div className="PointsPage-transactionType">
            {app.translator.trans(`enterprise-points.forum.transactions.types.${transaction.type()}`)}
          </div>
          <div className="PointsPage-transactionDescription">
            {transaction.description()}
          </div>
          <div className="PointsPage-transactionDate">
            {dayjs(transaction.createdAt()).format('YYYY-MM-DD HH:mm')}
          </div>
        </div>
        <div className="PointsPage-transactionBalance">
          {app.translator.trans('enterprise-points.forum.transactions.balance_after')}: {transaction.balanceAfter()}
        </div>
      </div>
    );
  }

  loadTransactions() {
    return app.store.find('pointTransactions', {
      filter: { user: app.session.user.id() },
      page: { limit: 20 }
    }).then(transactions => {
      this.transactions = transactions;
      this.loading = false;
      this.moreResults = transactions.length === 20;
      m.redraw();
    });
  }

  loadMore() {
    return app.store.find('pointTransactions', {
      filter: { user: app.session.user.id() },
      page: { offset: this.transactions.length, limit: 20 }
    }).then(transactions => {
      this.transactions = this.transactions.concat(transactions);
      this.moreResults = transactions.length === 20;
      m.redraw();
    });
  }
}

name: Tests

on:
    push: ~
    pull_request: ~

jobs:
    phpcs:
        name: PHPCS
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v2

            - uses: shivammathur/setup-php@v2
              with:
                  php-version: 8.0
                  extensions: curl, mbstring
                  coverage: none
                  tools: composer:v2, cs2pr

            - run: composer update --no-progress

            - run: vendor/bin/phpcs -q --report=checkstyle | cs2pr

    phpunit:
        name: PHPUnit on ${{ matrix.php }} ${{ matrix.composer-flags }}
        runs-on: ubuntu-latest
        strategy:
            matrix:
                php: ['7.3', '7.4']
                coverage: [pcov]
                composer-flags: ['']
                include:
                    - php: '7.2'
                      coverage: 'none'
                    - php: '8.0'
                      coverage: 'none'
                    - php: '8.1'
                      coverage: 'none'
                    - php: '8.2'
                      coverage: 'none'

        steps:
            - uses: actions/checkout@v2
              with:
                  fetch-depth: 0

            - uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}
                  extensions: curl
                  coverage: ${{ matrix.coverage }}
                  tools: composer:v2

            - run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

            - name: "Use PHPUnit 9.3+ on PHP 8+"
              run: composer require --no-update --dev phpunit/phpunit:^9.3
              if: ${{ matrix.php == '8.0' || matrix.php == '8.1' || matrix.php == '8.2' }}

            - run: composer update --no-progress ${{ matrix.composer-flags }}

            - run: vendor/bin/phpunit --no-coverage
              if: ${{ matrix.coverage == 'none' }}

            - run: vendor/bin/phpunit --coverage-text --coverage-clover=coverage.clover
              if: ${{ matrix.coverage != 'none' }}

            - run: php vendor/bin/ocular code-coverage:upload --format=php-clover coverage.clover
              if: ${{ matrix.coverage != 'none' && github.repository == 'dflydev/dflydev-fig-cookies' }}

    phpstan:
        name: PHPStan
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v2

            - uses: shivammathur/setup-php@v2
              with:
                  php-version: 8.0
                  extensions: curl
                  coverage: none
                  tools: composer:v2

            - run: composer update --no-progress

            - run: vendor/bin/phpstan analyse --no-progress

            - run: vendor/bin/phpstan analyse --no-progress -c phpstan.tests.neon

    psalm:
      name: Psalm
      runs-on: ubuntu-latest

      steps:
          - uses: actions/checkout@v2

          - uses: shivammathur/setup-php@v2
            with:
                php-version: 8.0
                extensions: curl
                coverage: none
                tools: composer:v2

          - run: composer update --no-progress

          - run: vendor/bin/psalm --no-progress --output-format=github

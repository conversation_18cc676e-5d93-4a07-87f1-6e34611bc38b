<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Tests\Integration;

use Enterprise\Points\Model\PointTransaction;
use Enterprise\Points\Service\PointsService;
use Flarum\Testing\integration\TestCase;
use Flarum\User\User;

class PointsServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->extension('enterprise-points');

        $this->prepareDatabase([
            'users' => [
                $this->normalUser(),
                ['id' => 3, 'username' => 'testuser2', 'email' => '<EMAIL>', 'is_email_confirmed' => 1],
            ],
        ]);
    }

    /**
     * @test
     */
    public function can_award_points_to_user()
    {
        $user = User::find(2);
        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $transaction = $pointsService->awardPoints(
            $user,
            10,
            PointTransaction::TYPE_POST,
            'Test post creation'
        );

        $this->assertEquals(10, $transaction->amount);
        $this->assertEquals(PointTransaction::TYPE_POST, $transaction->type);
        $this->assertEquals('Test post creation', $transaction->description);
        $this->assertEquals(10, $transaction->balance_after);

        // Check user points were updated
        $user->refresh();
        $this->assertEquals(10, $user->points);
        $this->assertEquals(10, $user->total_points_earned);
    }

    /**
     * @test
     */
    public function can_spend_points_from_user()
    {
        $user = User::find(2);
        $user->points = 20;
        $user->save();

        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $transaction = $pointsService->spendPoints(
            $user,
            5,
            PointTransaction::TYPE_STORE_PURCHASE,
            'Test store purchase'
        );

        $this->assertEquals(-5, $transaction->amount);
        $this->assertEquals(PointTransaction::TYPE_STORE_PURCHASE, $transaction->type);
        $this->assertEquals('Test store purchase', $transaction->description);
        $this->assertEquals(15, $transaction->balance_after);

        // Check user points were updated
        $user->refresh();
        $this->assertEquals(15, $user->points);
        $this->assertEquals(5, $user->total_points_spent);
    }

    /**
     * @test
     */
    public function cannot_spend_more_points_than_available()
    {
        $user = User::find(2);
        $user->points = 5;
        $user->save();

        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Insufficient points');

        $pointsService->spendPoints(
            $user,
            10,
            PointTransaction::TYPE_STORE_PURCHASE,
            'Test store purchase'
        );
    }

    /**
     * @test
     */
    public function can_transfer_points_between_users()
    {
        $sender = User::find(2);
        $recipient = User::find(3);
        
        $sender->points = 20;
        $sender->save();
        
        $recipient->points = 5;
        $recipient->save();

        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $transactions = $pointsService->transferPoints(
            $sender,
            $recipient,
            10,
            'Test transfer'
        );

        $this->assertCount(2, $transactions);

        // Check sender transaction
        $senderTransaction = $transactions[0];
        $this->assertEquals(-10, $senderTransaction->amount);
        $this->assertEquals(PointTransaction::TYPE_TRANSFER_SENT, $senderTransaction->type);
        $this->assertEquals(10, $senderTransaction->balance_after);

        // Check recipient transaction
        $recipientTransaction = $transactions[1];
        $this->assertEquals(10, $recipientTransaction->amount);
        $this->assertEquals(PointTransaction::TYPE_TRANSFER_RECEIVED, $recipientTransaction->type);
        $this->assertEquals(15, $recipientTransaction->balance_after);

        // Check user balances
        $sender->refresh();
        $recipient->refresh();
        
        $this->assertEquals(10, $sender->points);
        $this->assertEquals(15, $recipient->points);
    }

    /**
     * @test
     */
    public function cannot_transfer_points_to_self()
    {
        $user = User::find(2);
        $user->points = 20;
        $user->save();

        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot transfer points to yourself');

        $pointsService->transferPoints($user, $user, 10);
    }

    /**
     * @test
     */
    public function cannot_transfer_more_points_than_available()
    {
        $sender = User::find(2);
        $recipient = User::find(3);
        
        $sender->points = 5;
        $sender->save();

        $pointsService = $this->app()->getContainer()->make(PointsService::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Insufficient points');

        $pointsService->transferPoints($sender, $recipient, 10);
    }
}

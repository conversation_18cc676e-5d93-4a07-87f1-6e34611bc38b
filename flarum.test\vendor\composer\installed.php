<?php return array(
    'root' => array(
        'name' => 'flarum/flarum',
        'pretty_version' => 'v1.8.1',
        'version' => '*******',
        'reference' => NULL,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'axy/backtrace' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => 'e0f806986db00190e567b0071c765bd792360f06',
            'type' => 'library',
            'install_path' => __DIR__ . '/../axy/backtrace',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => '3c430083d0b41ceed84ecccf9dac613241d7305d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'components/font-awesome' => array(
            'pretty_version' => '5.15.4',
            'version' => '5.15.4.0',
            'reference' => 'e6fd09f30f578915cc0cf186b0dd0da54385b6be',
            'type' => 'component',
            'install_path' => __DIR__ . '/../components/font-awesome',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/fig-cookies' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'ebe6c15c9895fc490efe620ad734c8ef4a85bdb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/fig-cookies',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1ca8f21980e770095a31456042471a57bc4c68fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '2.13.9',
            'version' => '2.13.9.0',
            'reference' => 'c480849ca3ad6706a39c970cdfe6888fa8a058b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => '31610dbb31faa98e6b5447b62340826f54fbc4e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '95aa4cb529f1e96576f3fda9f5705ada4056a520',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => 'c268e882d4dbdd85e36e4ad69e02dc284f89d229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '8c784d071debd117328803d86b2097615b457500',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '2.1.25',
            'version' => '2.1.25.0',
            'reference' => '0dbf5d78455d4d6a41d186da50adc1122ec066f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.16.0',
            'version' => '2.16.0.0',
            'reference' => 'befcdc0e5dce67252aa6322d82424be928214fa2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/approval' => array(
            'pretty_version' => 'v1.8.2',
            'version' => '*******',
            'reference' => '61a76252b455d5dcad5535e9fef4a5445643d59a',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/approval',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/bbcode' => array(
            'pretty_version' => 'v1.8.0',
            'version' => '1.8.0.0',
            'reference' => '9d5fa06b18bf78c9d2f6e82d3904fff6e3fcb8a9',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/bbcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/core' => array(
            'pretty_version' => 'v1.8.10',
            'version' => '1.8.10.0',
            'reference' => '17b7ce60a524902a888708fee8b8878598c4cd0e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../flarum/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/emoji' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => 'edd6527e1014fd30779627f1c91461d12a9a1173',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/emoji',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/flags' => array(
            'pretty_version' => 'v1.8.2',
            'version' => '*******',
            'reference' => 'ba857db6f0e8c8b55cbab1ca0b7bc30215e92447',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/flags',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/flarum' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => NULL,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/lang-english' => array(
            'pretty_version' => 'v1.8.0',
            'version' => '1.8.0.0',
            'reference' => '7b1c2feb49f0b6707746907ca426dd309860b60f',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/lang-english',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/likes' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => '106cfa65076ba4be2da7af99f86eac99e5a38f4d',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/likes',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/lock' => array(
            'pretty_version' => 'v1.8.2',
            'version' => '*******',
            'reference' => 'f526df48dfa7b87aee0ca0b245000a3f3960cece',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/lock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/markdown' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => '22ea41d3be90d56c8b853c6798d25d2075e5f66f',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/mentions' => array(
            'pretty_version' => 'v1.8.5',
            'version' => '1.8.5.0',
            'reference' => 'cda26a4fbfa39b646c6d63394b281b004ddf69b6',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/mentions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/nicknames' => array(
            'pretty_version' => 'v1.8.2',
            'version' => '*******',
            'reference' => 'ab4ab706ea778f26fb923b31a3f3a2f76c5ecbc6',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/nicknames',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/pusher' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => 'dd1fb1da2d662ad2b9abb0757de7f0dc6fa7089f',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/pusher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/statistics' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => 'e907100ad530babb12c67f4959ae14e22f77b3fc',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/statistics',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/sticky' => array(
            'pretty_version' => 'v1.8.2',
            'version' => '*******',
            'reference' => 'a020d8d4619ce788990cf0c94f56424184048caa',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/sticky',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/subscriptions' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '*******',
            'reference' => '594100acf52ad74a33cbd30e5ca2220196790821',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/subscriptions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/suspend' => array(
            'pretty_version' => 'v1.8.4',
            'version' => '*******',
            'reference' => '8c62430d238f650eeb54323c204b335d9c39f771',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/suspend',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'flarum/tags' => array(
            'pretty_version' => 'v1.8.4',
            'version' => '*******',
            'reference' => 'c0c44ba3a913a2560cb75a6c31f0a78341114a12',
            'type' => 'flarum-extension',
            'install_path' => __DIR__ . '/../flarum/tags',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'franzl/whoops-middleware' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => '71d75c5fff75587d6194a051d510a9eca0e3a047',
            'type' => 'library',
            'install_path' => __DIR__ . '/../franzl/whoops-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'd2a8ae4bfd881086e55455e470776358eab27eae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/cache' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '7ae5b3661413dad7264b5c69037190d766bae50f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '705a4e1ef93cd492c45b9b3e7911cccc990a07f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/config' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'feac56ab7a5c70cf2dc60dffe4323eb9851f51a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '4aaa93223eb3bd8119157c95f58c022967826035',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '14062628d05f75047c5a1360b9350028427d568e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '5e0fd287a1b22a6b346a9f7cd484d8cf0234585d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/database' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '1a5b0e4e6913415464fa2aab554a38b9e6fa44b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/database',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'b7f06cafb6c09581617f2ca05d69e9b159e5a35d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '73db3e9a233ed587ba54f52ab8580f3c7bc872b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/hashing' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '2617f4de8d0150a3f8641b086fafac8c1e0cdbf2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/hashing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'aed81891a6e046fdee72edd497f822190f61c162',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/mail' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '557c01a4c6d3862829b004f198c1777a7f8fc35f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/mail',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '23aeff5b26ae4aee3f370835c76bd0f4e93f71d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/queue' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '0023daabf67743f7a2bd8328ca2b5537d93e4ae7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/queue',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/session' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '9c9988d7229d888c098eebbbb9fcb8c68580411c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/session',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '1c79242468d3bbd9a0f7477df34f9647dde2a09b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/translation' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'e119d1e55351bd846579c333dd24f9a042b724b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/validation' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => 'bb104f15545a55664755f58a278c7013f835918a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/validation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '*********',
            'reference' => '5e73eef48d9242532f81fadc14c816a01bfb1388',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jaybizzle/crawler-detect' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'reference' => 'b15237098211b502b9629bbf6f6884a3279420f2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jaybizzle/crawler-detect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jenssegers/agent' => array(
            'pretty_version' => 'v2.6.4',
            'version' => '2.6.4.0',
            'reference' => 'daa11c43729510b3700bc34d414664966b03bffe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jenssegers/agent',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-diactoros' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => 'aca73646e658dce3f079f6b8648c651e193e331e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-diactoros',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-escaper' => array(
            'pretty_version' => '2.12.0',
            'version' => '2.12.0.0',
            'reference' => 'ee7a4c37bf3d0e8c03635d5bddb5bb3184ead490',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-escaper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-httphandlerrunner' => array(
            'pretty_version' => '2.5.0',
            'version' => '2.5.0.0',
            'reference' => '7a47834aaad7852816d2ec4fdbb0492163b039ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-httphandlerrunner',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-stratigility' => array(
            'pretty_version' => '3.10.0',
            'version' => '3.10.0.0',
            'reference' => 'd45eec2f61b9706d9efcb398af53a196c3c7f301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-stratigility',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.7',
            'version' => '1.3.7.0',
            'reference' => '4f48ade902b94323ca3be7646db16209ec76be3d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.6.0',
            'version' => '2.6.0.0',
            'reference' => 'd150f911e0079e90ae3c106734c93137c184f932',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.1.10',
            'version' => '1.1.10.0',
            'reference' => '3239285c825c152bcc315fe0e87d6b55f5972ed1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'matthiasmullie/minify' => array(
            'pretty_version' => '1.3.73',
            'version' => '1.3.73.0',
            'reference' => 'cb7a9297b4ab070909cefade30ee95054d4ae87a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../matthiasmullie/minify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'matthiasmullie/path-converter' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'e7d13b2c7e2f2268e1424aaed02085518afa02d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../matthiasmullie/path-converter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'middlewares/base-path' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => '243ad6a6435cc74e221d81867d1110853ebf6321',
            'type' => 'library',
            'install_path' => __DIR__ . '/../middlewares/base-path',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'middlewares/base-path-router' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => '0f93a817c659084f9d8b70809d24e43b498ac502',
            'type' => 'library',
            'install_path' => __DIR__ . '/../middlewares/base-path-router',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'middlewares/request-handler' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => '732bc798d9ae11c2703b870ccd098030fb609c3b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../middlewares/request-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'middlewares/utils' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => '670b135ce0dbd040eadb025a9388f9bd617cc010',
            'type' => 'library',
            'install_path' => __DIR__ . '/../middlewares/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mobiledetect/mobiledetectlib' => array(
            'pretty_version' => '2.8.45',
            'version' => '2.8.45.0',
            'reference' => '96aaebcf4f50d3d2692ab81d2c5132e425bca266',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mobiledetect/mobiledetectlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '1.27.1',
            'version' => '1.27.1.0',
            'reference' => '904713c5929655dc9b97288b69cfeedad610c9a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.73.0',
            'version' => '2.73.0.0',
            'reference' => '9228ce90e1035ff2f0db84b40ec2e023ed802075',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.2.5',
            'version' => '1.2.5.0',
            'reference' => '0462f0166e823aad657c9224d0f849ecac1ba10a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd3ad0aa3b9f934602cb3e3902ebccf10be34d218',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v0.6.0',
            'version' => '0.6.0.0',
            'reference' => '31fa86924556b80735f98b294a7ffdfb26789f22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opis/closure' => array(
            'pretty_version' => '3.6.3',
            'version' => '3.6.3.0',
            'reference' => '3d81e4309d2a927abbe66df935f4bb60082805ad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.1 || ^2.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.1 || ^2.0',
            ),
        ),
        'psr/http-server-handler' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '84c4fb66179be4caaf8e97bd239203245302e7d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-middleware' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'c1481f747daaa6a0782775cd6a8c26a1bf4a3829',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
                1 => '1.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'pusher/pusher-php-server' => array(
            'pretty_version' => '2.6.4',
            'version' => '2.6.4.0',
            'reference' => '2cf2ba85e7ce3250468a1c42ab7c948a7d43839d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pusher/pusher-php-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'ad7475d1c9e70b190ecffc58f2d989416af339b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.8.1',
            'version' => '4.8.1.0',
            'reference' => 'fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.8.1',
            ),
        ),
        's9e/regexp-builder' => array(
            'pretty_version' => '1.4.6',
            'version' => '1.4.6.0',
            'reference' => '3a646bc7c40dba41903b7065f32230721e00df3a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../s9e/regexp-builder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        's9e/sweetdom' => array(
            'pretty_version' => '2.1.2',
            'version' => '2.1.2.0',
            'reference' => '482d42537a1e0ab98924a9d932b724a175302fe8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../s9e/sweetdom',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        's9e/text-formatter' => array(
            'pretty_version' => '2.14.3',
            'version' => '2.14.3.0',
            'reference' => 'bec02b24b78e4bc292c731b334d7cd31be72c3c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../s9e/text-formatter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'staudenmeir/eloquent-eager-limit' => array(
            'pretty_version' => 'v1.6.1',
            'version' => '1.6.1.0',
            'reference' => '439135c4b3361a313c2e7102d68bf807518d1bf9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staudenmeir/eloquent-eager-limit',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'swiftmailer/swiftmailer' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '6.3.0.0',
            'reference' => '8a5d5072dca8f48460fce2f4131fcc495eec654c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swiftmailer/swiftmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sycho/codecs-base64vlq' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => '210932edfb29049831e4def7f11a264944132ac9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sycho/codecs-base64vlq',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sycho/errors' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '82e955d247940aa7feed35e1ec7a61fc46639582',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sycho/errors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sycho/json-api' => array(
            'pretty_version' => 'v0.5.2',
            'version' => '0.5.2.0',
            'reference' => '5ef867317a6b39b307af0fc98c5b9c5828607301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sycho/json-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sycho/sourcemap' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => '81d514186e37efbea7f4dd701ea9133fd3412bf1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sycho/sourcemap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/config' => array(
            'pretty_version' => 'v5.4.46',
            'version' => '5.4.46.0',
            'reference' => '977c88a02d7d3f16904a81907531b19666a08e78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v5.4.47',
            'version' => '5.4.47.0',
            'reference' => 'c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'f1d00bddb83a4cb2138564b2150001cb6ce272b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '26954b3d62a6c5fd0ea8a2a00c0353a14978d05c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '72982eb416f61003e9bb6e91f8b3213600dcf9e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '7bc61cc2db649b4637d331240c5346dcc7708051',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '3d49eec03fda1f0fc19b7349fbbe55ebc1004214',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '63741784cd7b9967975eec610b256eed3ede022b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v5.4.48',
            'version' => '5.4.48.0',
            'reference' => '3f38b8af283b830e1363acd79e5bc3412d055341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '8c1b9b3e5b52981551fc6044539af1d974e39064',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-messageformatter' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'd2ed9f44f1ccb21d36e51ebb1f7b1ef26e36e0de',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-messageformatter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v5.4.47',
            'version' => '5.4.47.0',
            'reference' => '5d1662fb32ebc94f17ddb8d635454a776066733d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.4',
            'version' => '2.5.4.0',
            'reference' => 'f37b419f7aea2e9abf10abd261832cace12e3300',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'd9e72497367c23e08bf94176d2be45b00a9d232a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '98f26acc99341ca4bab345fb14d7b1d7cb825bed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v2.5.4',
            'version' => '2.5.4.0',
            'reference' => '450d4172653f38818657022252f9d81be89ee9a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3',
            ),
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => 'a454d47278cc16a5db371fe73ae66a78a633371e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => '0d72ac1c00084279c1816675284073c5a337c20d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'reference' => '87337c91b9dfacee02452244ee14ab3c43bc485a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wikimedia/less.php' => array(
            'pretty_version' => 'v3.2.1',
            'version' => '3.2.1.0',
            'reference' => '0d5b30ba792bdbf8991a646fc9c30561b38a5559',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wikimedia/less.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);

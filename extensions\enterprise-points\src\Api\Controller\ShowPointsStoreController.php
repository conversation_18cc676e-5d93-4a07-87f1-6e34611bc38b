<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\PointStoreItemSerializer;
use Enterprise\Points\Model\PointStoreItem;
use <PERSON>larum\Api\Controller\AbstractListController;
use Flarum\Http\RequestUtil;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for showing point store items.
 */
class ShowPointsStoreController extends AbstractListController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = PointStoreItemSerializer::class;

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $params = $request->getQueryParams();

        // Check if store is enabled
        if (!app('flarum.settings')->get('enterprise-points.enable_point_store', true)) {
            return [];
        }

        $query = PointStoreItem::active()->inStock()->bySortOrder();

        // Filter by type if specified
        if (isset($params['filter']['type'])) {
            $query->ofType($params['filter']['type']);
        }

        // For non-admin users, only show items they can afford or have no cost limit
        if (!$actor->hasPermission('enterprise-points.manageStore')) {
            $userPoints = $actor->points ?? 0;
            // You might want to show all items but indicate which ones are affordable
            // For now, we'll show all items
        }

        return $query->get();
    }
}

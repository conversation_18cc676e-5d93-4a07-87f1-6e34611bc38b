<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\PointStoreItemSerializer;
use Enterprise\Points\Model\PointStoreItem;
use <PERSON>larum\Api\Controller\AbstractShowController;
use Flarum\Http\RequestUtil;
use Flarum\User\Exception\PermissionDeniedException;
use Illuminate\Support\Arr;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for updating store items.
 */
class UpdateStoreItemController extends AbstractShowController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = PointStoreItemSerializer::class;

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $id = Arr::get($request->getQueryParams(), 'id');
        $attributes = Arr::get($request->getParsedBody(), 'data.attributes', []);

        // Check permissions
        if (!$actor->hasPermission('enterprise-points.manageStore')) {
            throw new PermissionDeniedException();
        }

        $storeItem = PointStoreItem::findOrFail($id);

        // Update fields if provided
        if (isset($attributes['name'])) {
            $storeItem->name = $attributes['name'];
        }

        if (isset($attributes['description'])) {
            $storeItem->description = $attributes['description'];
        }

        if (isset($attributes['cost'])) {
            $storeItem->cost = (int) $attributes['cost'];
        }

        if (isset($attributes['type'])) {
            $storeItem->type = $attributes['type'];
        }

        if (isset($attributes['icon'])) {
            $storeItem->icon = $attributes['icon'];
        }

        if (isset($attributes['color'])) {
            $storeItem->color = $attributes['color'];
        }

        if (isset($attributes['isActive'])) {
            $storeItem->is_active = (bool) $attributes['isActive'];
        }

        if (isset($attributes['stock'])) {
            $storeItem->stock = $attributes['stock'];
        }

        if (isset($attributes['purchaseLimit'])) {
            $storeItem->purchase_limit = $attributes['purchaseLimit'];
        }

        if (isset($attributes['metadata'])) {
            $storeItem->metadata = $attributes['metadata'];
        }

        if (isset($attributes['sortOrder'])) {
            $storeItem->sort_order = (int) $attributes['sortOrder'];
        }

        $storeItem->save();

        return $storeItem;
    }
}

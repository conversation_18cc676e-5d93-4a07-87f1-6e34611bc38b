import app from 'flarum/admin/app';
import ExtensionPage from 'flarum/admin/components/ExtensionPage';
import ItemList from 'flarum/common/utils/ItemList';
import Switch from 'flarum/common/components/Switch';
import Button from 'flarum/common/components/Button';

export default class PointsSettingsPage extends ExtensionPage {
  content() {
    return (
      <div className="PointsSettingsPage">
        <div className="container">
          <div className="PointsSettingsPage-header">
            <div className="PointsSettingsPage-headerIcon">
              <i className="fas fa-coins"></i>
            </div>
            <div className="PointsSettingsPage-headerText">
              <h2>{app.translator.trans('enterprise-points.admin.settings.title')}</h2>
              <p>Configure the enterprise points system for your forum.</p>
            </div>
          </div>

          <div className="PointsSettingsPage-content">
            <div className="Section">
              <div className="Section-header">
                <h3>Point Earning Rules</h3>
                <p>Configure how users can earn points through various activities.</p>
              </div>
              <div className="Section-body">
                {this.earningSettings().toArray()}
              </div>
            </div>

            <div className="Section">
              <div className="Section-header">
                <h3>Point Store Settings</h3>
                <p>Configure the point store and spending options.</p>
              </div>
              <div className="Section-body">
                {this.storeSettings().toArray()}
              </div>
            </div>

            <div className="Section">
              <div className="Section-header">
                <h3>Transfer Settings</h3>
                <p>Configure point transfers between users.</p>
              </div>
              <div className="Section-body">
                {this.transferSettings().toArray()}
              </div>
            </div>

            <div className="Section">
              <div className="Section-header">
                <h3>Actions</h3>
                <p>Manage points data and perform administrative actions.</p>
              </div>
              <div className="Section-body">
                {this.actionButtons().toArray()}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  earningSettings() {
    const items = new ItemList();

    items.add('points_per_post',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.points_per_post')}</label>
        <input
          className="FormControl"
          type="number"
          min="0"
          bidi={this.setting('enterprise-points.points_per_post', 10)}
        />
      </div>
    );

    items.add('points_per_reply',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.points_per_reply')}</label>
        <input
          className="FormControl"
          type="number"
          min="0"
          bidi={this.setting('enterprise-points.points_per_reply', 5)}
        />
      </div>
    );

    items.add('points_per_like_received',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.points_per_like_received')}</label>
        <input
          className="FormControl"
          type="number"
          min="0"
          bidi={this.setting('enterprise-points.points_per_like_received', 2)}
        />
      </div>
    );

    items.add('points_per_like_given',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.points_per_like_given')}</label>
        <input
          className="FormControl"
          type="number"
          min="0"
          bidi={this.setting('enterprise-points.points_per_like_given', 1)}
        />
      </div>
    );

    items.add('daily_login_points',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.daily_login_points')}</label>
        <input
          className="FormControl"
          type="number"
          min="0"
          bidi={this.setting('enterprise-points.daily_login_points', 5)}
        />
      </div>
    );

    return items;
  }

  storeSettings() {
    const items = new ItemList();

    items.add('enable_point_store',
      <div className="Form-group">
        {Switch.component({
          state: this.setting('enterprise-points.enable_point_store', true)(),
          onchange: this.setting('enterprise-points.enable_point_store', true),
        }, app.translator.trans('enterprise-points.admin.settings.enable_point_store'))}
      </div>
    );

    return items;
  }

  transferSettings() {
    const items = new ItemList();

    items.add('enable_point_transfer',
      <div className="Form-group">
        {Switch.component({
          state: this.setting('enterprise-points.enable_point_transfer', true)(),
          onchange: this.setting('enterprise-points.enable_point_transfer', true),
        }, app.translator.trans('enterprise-points.admin.settings.enable_point_transfer'))}
      </div>
    );

    items.add('min_transfer_amount',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.min_transfer_amount')}</label>
        <input
          className="FormControl"
          type="number"
          min="1"
          bidi={this.setting('enterprise-points.min_transfer_amount', 1)}
        />
      </div>
    );

    items.add('max_transfer_amount',
      <div className="Form-group">
        <label>{app.translator.trans('enterprise-points.admin.settings.max_transfer_amount')}</label>
        <input
          className="FormControl"
          type="number"
          min="1"
          bidi={this.setting('enterprise-points.max_transfer_amount', 1000)}
        />
      </div>
    );

    return items;
  }

  actionButtons() {
    const items = new ItemList();

    items.add('manage_store',
      <div className="Form-group">
        <Button
          className="Button Button--primary"
          onclick={() => m.route.set(app.route('extension', { id: 'enterprise-points' }) + '/store')}
        >
          <i className="fas fa-store"></i>
          Manage Store Items
        </Button>
      </div>
    );

    items.add('manage_rules',
      <div className="Form-group">
        <Button
          className="Button"
          onclick={() => m.route.set(app.route('extension', { id: 'enterprise-points' }) + '/rules')}
        >
          <i className="fas fa-cogs"></i>
          Manage Point Rules
        </Button>
      </div>
    );

    items.add('view_transactions',
      <div className="Form-group">
        <Button
          className="Button"
          onclick={() => m.route.set(app.route('extension', { id: 'enterprise-points' }) + '/transactions')}
        >
          <i className="fas fa-list"></i>
          View All Transactions
        </Button>
      </div>
    );

    return items;
  }
}

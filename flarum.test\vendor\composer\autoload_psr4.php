<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    's9e\\TextFormatter\\' => array($vendorDir . '/s9e/text-formatter/src'),
    's9e\\SweetDOM\\' => array($vendorDir . '/s9e/sweetdom/src'),
    's9e\\RegexpBuilder\\' => array($vendorDir . '/s9e/regexp-builder/src'),
    'axy\\sourcemap\\' => array($vendorDir . '/sycho/sourcemap/src'),
    'axy\\errors\\' => array($vendorDir . '/sycho/errors/src'),
    'axy\\codecs\\base64vlq\\' => array($vendorDir . '/sycho/codecs-base64vlq'),
    'axy\\backtrace\\tests\\' => array($vendorDir . '/axy/backtrace/tests'),
    'axy\\backtrace\\' => array($vendorDir . '/axy/backtrace/src'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Tobscure\\JsonApi\\' => array($vendorDir . '/sycho/json-api/src'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Symfony\\Polyfill\\Php81\\' => array($vendorDir . '/symfony/polyfill-php81'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Php73\\' => array($vendorDir . '/symfony/polyfill-php73'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\MessageFormatter\\' => array($vendorDir . '/symfony/polyfill-intl-messageformatter'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Iconv\\' => array($vendorDir . '/symfony/polyfill-iconv'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Component\\Config\\' => array($vendorDir . '/symfony/config'),
    'Staudenmeir\\EloquentEagerLimit\\' => array($vendorDir . '/staudenmeir/eloquent-eager-limit/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Server\\' => array($vendorDir . '/psr/http-server-handler/src', $vendorDir . '/psr/http-server-middleware/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src', $vendorDir . '/psr/http-factory/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'Opis\\Closure\\' => array($vendorDir . '/opis/closure/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Middlewares\\Utils\\' => array($vendorDir . '/middlewares/utils/src'),
    'Middlewares\\' => array($vendorDir . '/middlewares/request-handler/src', $vendorDir . '/middlewares/base-path-router/src', $vendorDir . '/middlewares/base-path/src'),
    'MatthiasMullie\\PathConverter\\' => array($vendorDir . '/matthiasmullie/path-converter/src'),
    'MatthiasMullie\\Minify\\' => array($vendorDir . '/matthiasmullie/minify/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laminas\\Stratigility\\' => array($vendorDir . '/laminas/laminas-stratigility/src'),
    'Laminas\\HttpHandlerRunner\\' => array($vendorDir . '/laminas/laminas-httphandlerrunner/src'),
    'Laminas\\Escaper\\' => array($vendorDir . '/laminas/laminas-escaper/src'),
    'Laminas\\Diactoros\\' => array($vendorDir . '/laminas/laminas-diactoros/src'),
    'Jenssegers\\Agent\\' => array($vendorDir . '/jenssegers/agent/src'),
    'Jaybizzle\\CrawlerDetect\\' => array($vendorDir . '/jaybizzle/crawler-detect/src'),
    'Intervention\\Image\\' => array($vendorDir . '/intervention/image/src/Intervention/Image'),
    'Illuminate\\View\\' => array($vendorDir . '/illuminate/view'),
    'Illuminate\\Validation\\' => array($vendorDir . '/illuminate/validation'),
    'Illuminate\\Translation\\' => array($vendorDir . '/illuminate/translation'),
    'Illuminate\\Support\\' => array($vendorDir . '/illuminate/macroable', $vendorDir . '/illuminate/collections', $vendorDir . '/illuminate/support'),
    'Illuminate\\Session\\' => array($vendorDir . '/illuminate/session'),
    'Illuminate\\Queue\\' => array($vendorDir . '/illuminate/queue'),
    'Illuminate\\Pipeline\\' => array($vendorDir . '/illuminate/pipeline'),
    'Illuminate\\Mail\\' => array($vendorDir . '/illuminate/mail'),
    'Illuminate\\Hashing\\' => array($vendorDir . '/illuminate/hashing'),
    'Illuminate\\Filesystem\\' => array($vendorDir . '/illuminate/filesystem'),
    'Illuminate\\Events\\' => array($vendorDir . '/illuminate/events'),
    'Illuminate\\Database\\' => array($vendorDir . '/illuminate/database'),
    'Illuminate\\Contracts\\' => array($vendorDir . '/illuminate/contracts'),
    'Illuminate\\Container\\' => array($vendorDir . '/illuminate/container'),
    'Illuminate\\Console\\' => array($vendorDir . '/illuminate/console'),
    'Illuminate\\Config\\' => array($vendorDir . '/illuminate/config'),
    'Illuminate\\Cache\\' => array($vendorDir . '/illuminate/cache'),
    'Illuminate\\Bus\\' => array($vendorDir . '/illuminate/bus'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Franzl\\Middleware\\Whoops\\' => array($vendorDir . '/franzl/whoops-middleware/src'),
    'Flarum\\Tags\\' => array($vendorDir . '/flarum/tags/src'),
    'Flarum\\Suspend\\' => array($vendorDir . '/flarum/suspend/src'),
    'Flarum\\Subscriptions\\' => array($vendorDir . '/flarum/subscriptions/src'),
    'Flarum\\Sticky\\' => array($vendorDir . '/flarum/sticky/src'),
    'Flarum\\Statistics\\' => array($vendorDir . '/flarum/statistics/src'),
    'Flarum\\Pusher\\' => array($vendorDir . '/flarum/pusher/src'),
    'Flarum\\Nicknames\\' => array($vendorDir . '/flarum/nicknames/src'),
    'Flarum\\Mentions\\' => array($vendorDir . '/flarum/mentions/src'),
    'Flarum\\Lock\\' => array($vendorDir . '/flarum/lock/src'),
    'Flarum\\Likes\\' => array($vendorDir . '/flarum/likes/src'),
    'Flarum\\Flags\\' => array($vendorDir . '/flarum/flags/src'),
    'Flarum\\BBCode\\' => array($vendorDir . '/flarum/bbcode/src'),
    'Flarum\\Approval\\' => array($vendorDir . '/flarum/approval/src'),
    'Flarum\\' => array($vendorDir . '/flarum/core/src'),
    'Fig\\Http\\Message\\' => array($vendorDir . '/fig/http-message-util/src'),
    'FastRoute\\' => array($vendorDir . '/nikic/fast-route/src'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/src'),
    'Doctrine\\DBAL\\' => array($vendorDir . '/doctrine/dbal/lib/Doctrine/DBAL'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/lib/Doctrine/Common/Lexer'),
    'Doctrine\\Common\\Cache\\' => array($vendorDir . '/doctrine/cache/lib/Doctrine/Common/Cache'),
    'Doctrine\\Common\\' => array($vendorDir . '/doctrine/event-manager/src'),
    'Dflydev\\FigCookies\\' => array($vendorDir . '/dflydev/fig-cookies/src/Dflydev/FigCookies'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
);

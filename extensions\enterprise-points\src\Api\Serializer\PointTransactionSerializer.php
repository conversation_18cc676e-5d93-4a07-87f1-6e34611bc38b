<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Serializer;

use Enterprise\Points\Model\PointTransaction;
use <PERSON>larum\Api\Serializer\AbstractSerializer;
use <PERSON>larum\Api\Serializer\BasicUserSerializer;
use Flarum\Api\Serializer\PostSerializer;
use Flarum\Api\Serializer\DiscussionSerializer;

/**
 * Serializer for point transactions.
 */
class PointTransactionSerializer extends AbstractSerializer
{
    /**
     * {@inheritdoc}
     */
    protected $type = 'pointTransactions';

    /**
     * {@inheritdoc}
     */
    protected function getDefaultAttributes($transaction)
    {
        /** @var PointTransaction $transaction */
        return [
            'id' => $transaction->id,
            'amount' => $transaction->amount,
            'type' => $transaction->type,
            'description' => $transaction->description,
            'metadata' => $transaction->metadata,
            'balanceAfter' => $transaction->balance_after,
            'createdAt' => $this->formatDate($transaction->created_at),
            'updatedAt' => $this->formatDate($transaction->updated_at),
            'formattedAmount' => $transaction->formatted_amount,
            'isEarning' => $transaction->isEarning(),
            'isSpending' => $transaction->isSpending(),
        ];
    }

    /**
     * @param PointTransaction $transaction
     * @return \Tobscure\JsonApi\Relationship
     */
    protected function user($transaction)
    {
        return $this->hasOne($transaction, BasicUserSerializer::class);
    }

    /**
     * @param PointTransaction $transaction
     * @return \Tobscure\JsonApi\Relationship|null
     */
    protected function relatedUser($transaction)
    {
        return $this->hasOne($transaction, BasicUserSerializer::class);
    }

    /**
     * @param PointTransaction $transaction
     * @return \Tobscure\JsonApi\Relationship|null
     */
    protected function relatedPost($transaction)
    {
        return $this->hasOne($transaction, PostSerializer::class);
    }

    /**
     * @param PointTransaction $transaction
     * @return \Tobscure\JsonApi\Relationship|null
     */
    protected function relatedDiscussion($transaction)
    {
        return $this->hasOne($transaction, DiscussionSerializer::class);
    }
}

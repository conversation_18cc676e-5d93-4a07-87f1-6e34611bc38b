<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\PointTransactionSerializer;
use Enterprise\Points\Service\PointsService;
use Flarum\Api\Controller\AbstractCreateController;
use Flarum\Http\RequestUtil;
use Flarum\User\Exception\PermissionDeniedException;
use Flarum\User\User;
use Illuminate\Support\Arr;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for creating point transactions (admin adjustments, transfers).
 */
class CreatePointTransactionController extends AbstractCreateController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = PointTransactionSerializer::class;

    /**
     * {@inheritdoc}
     */
    public $include = ['user', 'relatedUser'];

    /**
     * @var PointsService
     */
    protected $pointsService;

    /**
     * @param PointsService $pointsService
     */
    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $attributes = Arr::get($request->getParsedBody(), 'data.attributes', []);

        $type = Arr::get($attributes, 'type');
        $amount = (int) Arr::get($attributes, 'amount');
        $userId = (int) Arr::get($attributes, 'userId');
        $description = Arr::get($attributes, 'description');
        $recipientId = Arr::get($attributes, 'recipientId');

        // Validate required fields
        if (!$type || !$amount || !$userId) {
            throw new \InvalidArgumentException('Missing required fields: type, amount, userId');
        }

        $user = User::findOrFail($userId);

        switch ($type) {
            case 'admin_adjustment':
                // Only admins can make adjustments
                if (!$actor->hasPermission('enterprise-points.adjustPoints')) {
                    throw new PermissionDeniedException();
                }

                if ($amount > 0) {
                    return $this->pointsService->awardPoints(
                        $user,
                        $amount,
                        'admin_adjustment',
                        $description ?: "Admin adjustment by {$actor->display_name}",
                        ['admin_id' => $actor->id],
                        $actor
                    );
                } else {
                    return $this->pointsService->spendPoints(
                        $user,
                        abs($amount),
                        'admin_adjustment',
                        $description ?: "Admin adjustment by {$actor->display_name}",
                        ['admin_id' => $actor->id],
                        $actor
                    );
                }

            case 'transfer':
                // Check if transfers are enabled
                if (!app('flarum.settings')->get('enterprise-points.enable_point_transfer', true)) {
                    throw new \Exception('Point transfers are disabled');
                }

                // Users can only transfer their own points
                if ($user->id !== $actor->id) {
                    throw new PermissionDeniedException();
                }

                if (!$recipientId) {
                    throw new \InvalidArgumentException('Recipient ID is required for transfers');
                }

                $recipient = User::findOrFail($recipientId);

                // Check transfer limits
                $minAmount = (int) app('flarum.settings')->get('enterprise-points.min_transfer_amount', 1);
                $maxAmount = (int) app('flarum.settings')->get('enterprise-points.max_transfer_amount', 1000);

                if ($amount < $minAmount || $amount > $maxAmount) {
                    throw new \InvalidArgumentException("Transfer amount must be between {$minAmount} and {$maxAmount}");
                }

                $transactions = $this->pointsService->transferPoints($user, $recipient, $amount, $description);
                
                // Return the sender's transaction
                return $transactions[0];

            default:
                throw new \InvalidArgumentException('Invalid transaction type');
        }
    }
}

{"packages": [{"name": "axy/backtrace", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/axypro/backtrace.git", "reference": "e0f806986db00190e567b0071c765bd792360f06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/axypro/backtrace/zipball/e0f806986db00190e567b0071c765bd792360f06", "reference": "e0f806986db00190e567b0071c765bd792360f06", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "require-dev": {"phpmd/phpmd": "^2.6", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.4"}, "time": "2019-02-02T18:01:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"axy\\backtrace\\": "src", "axy\\backtrace\\tests\\": "tests"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tracing in PHP", "homepage": "https://github.com/axypro/backtrace", "keywords": ["Backtrace", "debug", "exception", "trace"], "support": {"issues": "https://github.com/axypro/backtrace/issues", "source": "https://github.com/axypro/backtrace/tree/2.0.0"}, "install-path": "../axy/backtrace"}, {"name": "brick/math", "version": "0.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "time": "2023-01-15T23:15:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "install-path": "../brick/math"}, {"name": "carbonphp/carbon-doctrine-types", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/3c430083d0b41ceed84ecccf9dac613241d7305d", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.8 || ^8.0"}, "conflict": {"doctrine/dbal": ">=3.7.0"}, "require-dev": {"doctrine/dbal": ">=2.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "time": "2023-10-01T12:35:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/1.0.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "install-path": "../carbonphp/carbon-doctrine-types"}, {"name": "components/font-awesome", "version": "5.15.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/components/font-awesome.git", "reference": "e6fd09f30f578915cc0cf186b0dd0da54385b6be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/font-awesome/zipball/e6fd09f30f578915cc0cf186b0dd0da54385b6be", "reference": "e6fd09f30f578915cc0cf186b0dd0da54385b6be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "time": "2021-08-15T10:31:24+00:00", "type": "component", "extra": {"component": {"files": ["css/all.min.css", "webfonts/*"], "styles": ["css/all.css"]}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["CC-BY-4.0", "MIT", "OFL-1.1"], "description": "Font Awesome, the iconic SVG, font, and CSS framework.", "support": {"issues": "https://github.com/components/font-awesome/issues", "source": "https://github.com/components/font-awesome/tree/5.15.4"}, "install-path": "../components/font-awesome"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "time": "2024-07-08T12:26:09+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "install-path": "../dflydev/dot-access-data"}, {"name": "dflydev/fig-cookies", "version": "v3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-fig-cookies.git", "reference": "ebe6c15c9895fc490efe620ad734c8ef4a85bdb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-fig-cookies/zipball/ebe6c15c9895fc490efe620ad734c8ef4a85bdb0", "reference": "ebe6c15c9895fc490efe620ad734c8ef4a85bdb0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pcre": "*", "php": "^7.2 || ^8.0", "psr/http-message": "^1.0.1 || ^2"}, "require-dev": {"doctrine/coding-standard": "^8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12.16", "phpunit/phpunit": "^7.2.6 || ^9", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.3", "vimeo/psalm": "^4.4"}, "time": "2023-07-18T20:41:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dflydev\\FigCookies\\": "src/Dflydev/FigCookies"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cookies for PSR-7 HTTP Message Interface.", "keywords": ["cookies", "psr-7", "psr7"], "support": {"issues": "https://github.com/dflydev/dflydev-fig-cookies/issues", "source": "https://github.com/dflydev/dflydev-fig-cookies/tree/v3.1.0"}, "install-path": "../dflydev/fig-cookies"}, {"name": "doctrine/cache", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "time": "2022-05-20T20:07:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "install-path": "../doctrine/cache"}, {"name": "doctrine/dbal", "version": "2.13.9", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "time": "2022-05-02T20:28:55+00:00", "bin": ["bin/doctrine-dbal"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "install-path": "../doctrine/dbal"}, {"name": "doctrine/deprecations", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/31610dbb31faa98e6b5447b62340826f54fbc4e9", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "1.4.10 || 2.0.3", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "time": "2024-12-07T21:18:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.4"}, "install-path": "../doctrine/deprecations"}, {"name": "doctrine/event-manager", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "time": "2022-10-12T20:51:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "install-path": "../doctrine/event-manager"}, {"name": "doctrine/inflector", "version": "2.0.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "time": "2024-02-18T20:23:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "install-path": "../doctrine/inflector"}, {"name": "doctrine/lexer", "version": "1.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "time": "2022-02-28T11:07:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "install-path": "../doctrine/lexer"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "time": "2024-10-09T13:47:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "install-path": "../dragonmantank/cron-expression"}, {"name": "egulias/email-validator", "version": "2.1.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "time": "2020-12-29T14:50:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "install-path": "../egulias/email-validator"}, {"name": "fig/http-message-util", "version": "1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message-util/zipball/9d94dc0154230ac39e5bf89398b324a86f63f765", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "time": "2020-11-24T22:02:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "install-path": "../fig/http-message-util"}, {"name": "filp/whoops", "version": "2.16.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/befcdc0e5dce67252aa6322d82424be928214fa2", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "time": "2024-09-25T12:00:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.16.0"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "install-path": "../filp/whoops"}, {"name": "flarum/approval", "version": "v1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/approval.git", "reference": "61a76252b455d5dcad5535e9fef4a5445643d59a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/approval/zipball/61a76252b455d5dcad5535e9fef4a5445643d59a", "reference": "61a76252b455d5dcad5535e9fef4a5445643d59a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8", "flarum/flags": "^1.7"}, "require-dev": {"flarum/testing": "^1.0.0"}, "time": "2024-10-02T11:13:26+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-check", "color": "#3F8A32", "backgroundColor": "#ABDC88"}, "title": "Approval", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Approval\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Make discussions and posts require moderator approval.", "homepage": "https://flarum.org", "keywords": ["moderation"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/approval"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/approval"}, {"name": "flarum/bbcode", "version": "v1.8.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/bbcode.git", "reference": "9d5fa06b18bf78c9d2f6e82d3904fff6e3fcb8a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/bbcode/zipball/9d5fa06b18bf78c9d2f6e82d3904fff6e3fcb8a9", "reference": "9d5fa06b18bf78c9d2f6e82d3904fff6e3fcb8a9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "time": "2023-05-20T15:08:42+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": false, "css": false, "admin": false, "forum": false, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": false, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": false, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-bold", "color": "#fff", "backgroundColor": "#238C59"}, "title": "BBCode", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\BBCode\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow posts to be formatted with BBCode.", "homepage": "https://flarum.org", "keywords": ["formatting"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/bbcode"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/bbcode"}, {"name": "flarum/core", "version": "v1.8.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/flarum/flarum-core.git", "reference": "17b7ce60a524902a888708fee8b8878598c4cd0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/flarum-core/zipball/17b7ce60a524902a888708fee8b8878598c4cd0e", "reference": "17b7ce60a524902a888708fee8b8878598c4cd0e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"components/font-awesome": "^5.14.0", "dflydev/fig-cookies": "^3.0.0", "doctrine/dbal": "^2.7", "dragonmantank/cron-expression": "^3.1.0", "franzl/whoops-middleware": "^2.0.0", "guzzlehttp/guzzle": "^6.0|^7.4", "illuminate/bus": "^8.0", "illuminate/cache": "^8.0", "illuminate/config": "^8.0", "illuminate/console": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/database": "^8.0", "illuminate/events": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/hashing": "^8.0", "illuminate/mail": "^8.0", "illuminate/queue": "^8.0", "illuminate/session": "^8.0", "illuminate/support": "^8.0", "illuminate/validation": "^8.0", "illuminate/view": "^8.0", "intervention/image": "2.5.* || ^2.6.1", "jenssegers/agent": "^2.6", "laminas/laminas-diactoros": "^2.4.1 || ^3.0.0", "laminas/laminas-httphandlerrunner": "^1.2.0 || ^2.3.0 || ^3.0.0", "laminas/laminas-stratigility": "^3.2.2 || ^4.0.0", "league/flysystem": "^1.0.11", "matthiasmullie/minify": "^1.3", "middlewares/base-path": "^2.0.1", "middlewares/base-path-router": "^2.0.1", "middlewares/request-handler": "^2.0.1", "monolog/monolog": "^1.16.0", "nesbot/carbon": "^2.0", "nikic/fast-route": "^0.6", "php": ">=7.3 || ^8.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "s9e/text-formatter": ">=2.3.6 <2.15", "staudenmeir/eloquent-eager-limit": "^1.0", "sycho/json-api": "^0.5.0", "sycho/sourcemap": "^2.0.0", "symfony/config": "^5.2.2", "symfony/console": "^5.2.2", "symfony/event-dispatcher": "^5.2.2", "symfony/mime": "^5.2.0", "symfony/polyfill-intl-messageformatter": "^1.22.0", "symfony/translation": "^5.1.5", "symfony/yaml": "^5.2.2", "wikimedia/less.php": "^3.2"}, "require-dev": {"flarum/testing": "^1.0.0"}, "time": "2025-03-12T10:18:56+00:00", "type": "library", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": true, "prettier": true, "typescript": true, "bundlewatch": true, "editorConfig": true, "githubActions": true, "backendTesting": true}, "excludeScaffolding": ["LICENSE.md", "js/tsconfig.json", "js/webpack.config.js"]}, "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Flarum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Flarum", "email": "<EMAIL>", "homepage": "https://flarum.org/team"}], "description": "Delightfully simple forum software.", "homepage": "https://flarum.org/", "keywords": ["discussion", "forum"], "support": {"chat": "https://flarum.org/chat", "docs": "https://docs.flarum.org", "forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/flarum-core"}, "funding": [{"url": "https://github.com/sponsors/flarum", "type": "github"}, {"url": "https://opencollective.com/flarum", "type": "opencollective"}, {"url": "https://flarum.org/donate", "type": "other"}], "install-path": "../flarum/core"}, {"name": "flarum/emoji", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/emoji.git", "reference": "edd6527e1014fd30779627f1c91461d12a9a1173"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/emoji/zipball/edd6527e1014fd30779627f1c91461d12a9a1173", "reference": "edd6527e1014fd30779627f1c91461d12a9a1173", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "time": "2024-10-02T07:10:53+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": false, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"image": "icon.svg", "backgroundColor": "#FECC4D"}, "title": "<PERSON><PERSON><PERSON>", "category": "feature"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Convert text and unicode emoji into Twemoji.", "homepage": "https://flarum.org", "keywords": ["formatting"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/emoji"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/emoji"}, {"name": "flarum/flags", "version": "v1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/flags.git", "reference": "ba857db6f0e8c8b55cbab1ca0b7bc30215e92447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/flags/zipball/ba857db6f0e8c8b55cbab1ca0b7bc30215e92447", "reference": "ba857db6f0e8c8b55cbab1ca0b7bc30215e92447", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8.6"}, "require-dev": {"flarum/core": "*@dev", "flarum/tags": "*@dev", "flarum/testing": "^1.0.0"}, "time": "2024-10-26T22:18:59+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": true, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-flag", "color": "#fff", "backgroundColor": "#D659B5"}, "title": "Flags", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Flags\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow users to flag posts for moderator review.", "homepage": "https://flarum.org", "keywords": ["moderation"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/flags"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/flags"}, {"name": "flarum/lang-english", "version": "v1.8.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/lang-english.git", "reference": "7b1c2feb49f0b6707746907ca426dd309860b60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/lang-english/zipball/7b1c2feb49f0b6707746907ca426dd309860b60f", "reference": "7b1c2feb49f0b6707746907ca426dd309860b60f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "time": "2023-05-20T15:08:42+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": false, "css": false, "admin": false, "forum": false, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": false, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": false, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-locale": {"code": "en", "title": "English"}, "flarum-extension": {"icon": {"image": "icon.svg", "backgroundSize": "cover", "backgroundColor": "#00247d", "backgroundPosition": "center"}, "info": {"donate": "https://flarum.org/donate/", "support": "https://discuss.flarum.org", "website": "https://flarum.org"}, "title": "English", "category": "language"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "English language pack.", "keywords": ["locale"], "support": {"source": "https://github.com/flarum/lang-english/tree/v1.8.0"}, "funding": [{"url": "https://github.com/flarum", "type": "github"}, {"url": "https://opencollective.com/flarum", "type": "open_collective"}], "install-path": "../flarum/lang-english"}, {"name": "flarum/likes", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/likes.git", "reference": "106cfa65076ba4be2da7af99f86eac99e5a38f4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/likes/zipball/106cfa65076ba4be2da7af99f86eac99e5a38f4d", "reference": "106cfa65076ba4be2da7af99f86eac99e5a38f4d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/testing": "^1.0.0"}, "time": "2024-10-02T11:13:26+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "far fa-thumbs-up", "color": "#fff", "backgroundColor": "#3A649D"}, "title": "<PERSON>s", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Likes\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow users to like posts.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/likes"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/likes"}, {"name": "flarum/lock", "version": "v1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/lock.git", "reference": "f526df48dfa7b87aee0ca0b245000a3f3960cece"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/lock/zipball/f526df48dfa7b87aee0ca0b245000a3f3960cece", "reference": "f526df48dfa7b87aee0ca0b245000a3f3960cece", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "time": "2024-10-26T22:15:50+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-lock", "color": "#666", "backgroundColor": "#ddd"}, "title": "Lock", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Lock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "End a discussion and don't let anyone add further replies.", "homepage": "https://flarum.org", "keywords": ["moderation"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/lock"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/lock"}, {"name": "flarum/markdown", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/markdown.git", "reference": "22ea41d3be90d56c8b853c6798d25d2075e5f66f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/markdown/zipball/22ea41d3be90d56c8b853c6798d25d2075e5f66f", "reference": "22ea41d3be90d56c8b853c6798d25d2075e5f66f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "time": "2024-10-02T07:10:53+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": false, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"color": "#fff", "image": "icon.svg", "backgroundSize": "100%", "backgroundColor": "#000", "backgroundRepeat": "no-repeat", "backgroundPosition": "center"}, "title": "<PERSON><PERSON>", "category": "feature"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow posts to be formatted with <PERSON><PERSON>.", "homepage": "https://flarum.org", "keywords": ["formatting"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/markdown"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/markdown"}, {"name": "flarum/mentions", "version": "v1.8.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/mentions.git", "reference": "cda26a4fbfa39b646c6d63394b281b004ddf69b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/mentions/zipball/cda26a4fbfa39b646c6d63394b281b004ddf69b6", "reference": "cda26a4fbfa39b646c6d63394b281b004ddf69b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/core": "*@dev", "flarum/tags": "*@dev", "flarum/testing": "^1.0.0"}, "time": "2024-11-06T14:59:27+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-at", "color": "#fff", "backgroundColor": "#539EC1"}, "title": "Mentions", "category": "feature", "optional-dependencies": ["flarum/tags"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Mentions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Mention and reply to specific posts and users.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/mentions"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/mentions"}, {"name": "flarum/nicknames", "version": "v1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/nicknames.git", "reference": "ab4ab706ea778f26fb923b31a3f3a2f76c5ecbc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/nicknames/zipball/ab4ab706ea778f26fb923b31a3f3a2f76c5ecbc6", "reference": "ab4ab706ea778f26fb923b31a3f3a2f76c5ecbc6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/core": "1.x-dev", "flarum/testing": "^1.0.0"}, "time": "2024-10-26T22:15:50+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": false, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-user-tag", "color": "#ffffff", "backgroundColor": "#8E4529"}, "title": "Nicknames", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Nicknames\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow users to set nicknames.", "homepage": "https://flarum.org", "keywords": ["nicknames"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/nicknames"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/nicknames"}, {"name": "flarum/pusher", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/pusher.git", "reference": "dd1fb1da2d662ad2b9abb0757de7f0dc6fa7089f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/pusher/zipball/dd1fb1da2d662ad2b9abb0757de7f0dc6fa7089f", "reference": "dd1fb1da2d662ad2b9abb0757de7f0dc6fa7089f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8", "pusher/pusher-php-server": "^2.2"}, "require-dev": {"flarum/tags": "^1.0"}, "time": "2024-09-29T14:35:29+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": true, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": false}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"color": "#fff", "image": "icon.png", "backgroundSize": "46% 63%", "backgroundColor": "#40bad8", "backgroundRepeat": "no-repeat", "backgroundPosition": "center"}, "title": "<PERSON><PERSON><PERSON>", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Pusher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "See new discussions and posts in real-time using <PERSON><PERSON><PERSON>.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/pusher"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/pusher"}, {"name": "flarum/statistics", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/statistics.git", "reference": "e907100ad530babb12c67f4959ae14e22f77b3fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/statistics/zipball/e907100ad530babb12c67f4959ae14e22f77b3fc", "reference": "e907100ad530babb12c67f4959ae14e22f77b3fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/testing": "^1.0.0"}, "time": "2024-10-02T07:10:53+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": false, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": true, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}, "excludeScaffolding": ["js/src/admin/index.ts"]}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-chart-bar", "color": "#fff", "backgroundColor": "#6932d1"}, "title": "Statistics", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Statistics\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add a basic statistics widget on the Dashboard.", "homepage": "https://flarum.org", "keywords": ["administration"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/statistics"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/statistics"}, {"name": "flarum/sticky", "version": "v1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/sticky.git", "reference": "a020d8d4619ce788990cf0c94f56424184048caa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/sticky/zipball/a020d8d4619ce788990cf0c94f56424184048caa", "reference": "a020d8d4619ce788990cf0c94f56424184048caa", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/tags": "*@dev", "flarum/testing": "^1.0.0"}, "time": "2024-10-26T22:15:50+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-thumbtack", "color": "#fff", "backgroundColor": "#D13E32"}, "title": "<PERSON>y", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Sticky\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "<PERSON><PERSON> discussions to the top of the list.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/sticky"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/sticky"}, {"name": "flarum/subscriptions", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/subscriptions.git", "reference": "594100acf52ad74a33cbd30e5ca2220196790821"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/subscriptions/zipball/594100acf52ad74a33cbd30e5ca2220196790821", "reference": "594100acf52ad74a33cbd30e5ca2220196790821", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/approval": "@dev", "flarum/testing": "^1.0.0"}, "time": "2024-10-02T07:10:53+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": false, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-star", "color": "#de8e00", "backgroundColor": "#ffea7b"}, "title": "Subscriptions", "category": "feature", "optional-dependencies": ["flarum/approval"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Subscriptions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Allow users to follow discussions and receive notifications for new posts.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/subscriptions"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/subscriptions"}, {"name": "flarum/suspend", "version": "v1.8.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/suspend.git", "reference": "8c62430d238f650eeb54323c204b335d9c39f771"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/suspend/zipball/8c62430d238f650eeb54323c204b335d9c39f771", "reference": "8c62430d238f650eeb54323c204b335d9c39f771", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/testing": "^1.0.0"}, "time": "2024-11-20T08:51:17+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": false, "prettier": true, "typescript": false, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-ban", "color": "#666", "backgroundColor": "#ddd"}, "title": "Suspend", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Suspend\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Suspend users so they can't post.", "homepage": "https://flarum.org", "keywords": ["moderation"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/suspend"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/suspend"}, {"name": "flarum/tags", "version": "v1.8.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/flarum/tags.git", "reference": "c0c44ba3a913a2560cb75a6c31f0a78341114a12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flarum/tags/zipball/c0c44ba3a913a2560cb75a6c31f0a78341114a12", "reference": "c0c44ba3a913a2560cb75a6c31f0a78341114a12", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"flarum/core": "^1.8"}, "require-dev": {"flarum/core": "*@dev", "flarum/testing": "^1.0.0"}, "time": "2025-03-12T10:05:43+00:00", "type": "flarum-extension", "extra": {"flarum-cli": {"modules": {"js": true, "css": true, "admin": true, "forum": true, "gitConf": true, "styleci": true, "jsCommon": true, "prettier": false, "typescript": true, "bundlewatch": false, "editorConfig": true, "githubActions": true, "backendTesting": true}}, "branch-alias": {"dev-main": "1.x-dev"}, "flarum-extension": {"icon": {"name": "fas fa-tags", "color": "#fff", "backgroundColor": "#F28326"}, "title": "Tags", "category": "feature"}}, "installation-source": "dist", "autoload": {"psr-4": {"Flarum\\Tags\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Organize discussions into a hierarchy of tags and categories.", "homepage": "https://flarum.org", "keywords": ["discussion"], "support": {"forum": "https://discuss.flarum.org", "issues": "https://github.com/flarum/framework/issues", "source": "https://github.com/flarum/tags"}, "funding": [{"url": "https://flarum.org/donate/", "type": "website"}], "install-path": "../flarum/tags"}, {"name": "franzl/whoops-middleware", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/franzliedke/whoops-middleware.git", "reference": "71d75c5fff75587d6194a051d510a9eca0e3a047"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/franzliedke/whoops-middleware/zipball/71d75c5fff75587d6194a051d510a9eca0e3a047", "reference": "71d75c5fff75587d6194a051d510a9eca0e3a047", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"filp/whoops": "^1.1 || ^2.0", "middlewares/utils": "^3.0", "php": "^7.2 || ^8.0"}, "require-dev": {"laminas/laminas-diactoros": "^1.1.0 || ^2.0", "phpunit/phpunit": "^8.0", "psr/http-server-middleware": "^1.0"}, "suggest": {"psr/http-server-middleware": "In case you want to use PSR 15 middleware"}, "time": "2021-01-03T01:30:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Franzl\\Middleware\\Whoops\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PSR-15 compatible middleware for Whoops, the pretty error handler", "support": {"issues": "https://github.com/franzliedke/whoops-middleware/issues", "source": "https://github.com/franzliedke/whoops-middleware/tree/2.0.0"}, "install-path": "../franzl/whoops-middleware"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2024-07-24T11:22:20+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2025-03-27T13:27:01+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2025-03-27T12:30:47+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "illuminate/bus", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "d2a8ae4bfd881086e55455e470776358eab27eae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/d2a8ae4bfd881086e55455e470776358eab27eae", "reference": "d2a8ae4bfd881086e55455e470776358eab27eae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/pipeline": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "time": "2022-03-07T15:02:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/bus"}, {"name": "illuminate/cache", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/cache.git", "reference": "7ae5b3661413dad7264b5c69037190d766bae50f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/cache/zipball/7ae5b3661413dad7264b5c69037190d766bae50f", "reference": "7ae5b3661413dad7264b5c69037190d766bae50f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "provide": {"psr/simple-cache-implementation": "1.0"}, "suggest": {"ext-memcached": "Required to use the memcache cache driver.", "illuminate/database": "Required to use the database cache driver (^8.0).", "illuminate/filesystem": "Required to use the file cache driver (^8.0).", "illuminate/redis": "Required to use the redis cache driver (^8.0).", "symfony/cache": "Required to PSR-6 cache bridge (^5.4)."}, "time": "2022-07-22T14:58:32+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Cache package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/cache"}, {"name": "illuminate/collections", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "705a4e1ef93cd492c45b9b3e7911cccc990a07f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/705a4e1ef93cd492c45b9b3e7911cccc990a07f4", "reference": "705a4e1ef93cd492c45b9b3e7911cccc990a07f4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "php": "^7.3|^8.0"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^5.4)."}, "time": "2022-06-23T15:29:49+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/collections"}, {"name": "illuminate/config", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/config.git", "reference": "feac56ab7a5c70cf2dc60dffe4323eb9851f51a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/config/zipball/feac56ab7a5c70cf2dc60dffe4323eb9851f51a8", "reference": "feac56ab7a5c70cf2dc60dffe4323eb9851f51a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "php": "^7.3|^8.0"}, "time": "2022-01-31T15:57:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Config\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Config package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/config"}, {"name": "illuminate/console", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/console.git", "reference": "4aaa93223eb3bd8119157c95f58c022967826035"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/console/zipball/4aaa93223eb3bd8119157c95f58c022967826035", "reference": "4aaa93223eb3bd8119157c95f58c022967826035", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0", "symfony/console": "^5.4", "symfony/process": "^5.4"}, "suggest": {"dragonmantank/cron-expression": "Required to use scheduler (^3.0.2).", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (^6.5.5|^7.0.1).", "illuminate/bus": "Required to use the scheduled job dispatcher (^8.0).", "illuminate/container": "Required to use the scheduler (^8.0).", "illuminate/filesystem": "Required to use the generator command (^8.0).", "illuminate/queue": "Required to use closures for scheduled jobs (^8.0)."}, "time": "2022-04-21T22:14:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Console package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/console"}, {"name": "illuminate/container", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "14062628d05f75047c5a1360b9350028427d568e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/14062628d05f75047c5a1360b9350028427d568e", "reference": "14062628d05f75047c5a1360b9350028427d568e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "^8.0", "php": "^7.3|^8.0", "psr/container": "^1.0"}, "provide": {"psr/container-implementation": "1.0"}, "time": "2022-02-02T21:03:35+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/container"}, {"name": "illuminate/contracts", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "5e0fd287a1b22a6b346a9f7cd484d8cf0234585d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/5e0fd287a1b22a6b346a9f7cd484d8cf0234585d", "reference": "5e0fd287a1b22a6b346a9f7cd484d8cf0234585d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.3|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0"}, "time": "2022-01-13T14:47:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/contracts"}, {"name": "illuminate/database", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "1a5b0e4e6913415464fa2aab554a38b9e6fa44b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/1a5b0e4e6913415464fa2aab554a38b9e6fa44b1", "reference": "1a5b0e4e6913415464fa2aab554a38b9e6fa44b1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0", "symfony/console": "^5.4"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "illuminate/console": "Required to use the database commands (^8.0).", "illuminate/events": "Required to use the observers with Eloquent (^8.0).", "illuminate/filesystem": "Required to use the migrations (^8.0).", "illuminate/pagination": "Required to paginate the result set (^8.0).", "symfony/finder": "Required to use Eloquent model factories (^5.4)."}, "time": "2022-08-31T16:16:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/database"}, {"name": "illuminate/events", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "b7f06cafb6c09581617f2ca05d69e9b159e5a35d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/b7f06cafb6c09581617f2ca05d69e9b159e5a35d", "reference": "b7f06cafb6c09581617f2ca05d69e9b159e5a35d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/bus": "^8.0", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "time": "2021-09-15T14:32:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/events"}, {"name": "illuminate/filesystem", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "73db3e9a233ed587ba54f52ab8580f3c7bc872b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/73db3e9a233ed587ba54f52ab8580f3c7bc872b2", "reference": "73db3e9a233ed587ba54f52ab8580f3c7bc872b2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0", "symfony/finder": "^5.4"}, "suggest": {"ext-ftp": "Required to use the Flysystem FTP driver.", "illuminate/http": "Required for handling uploaded files (^7.0).", "league/flysystem": "Required to use the Flysystem local and FTP drivers (^1.1).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^5.4).", "symfony/mime": "Required to enable support for guessing extensions (^5.4)."}, "time": "2022-01-15T15:00:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/filesystem"}, {"name": "illuminate/hashing", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/hashing.git", "reference": "2617f4de8d0150a3f8641b086fafac8c1e0cdbf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/hashing/zipball/2617f4de8d0150a3f8641b086fafac8c1e0cdbf2", "reference": "2617f4de8d0150a3f8641b086fafac8c1e0cdbf2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "time": "2021-10-22T13:20:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Hashing\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Hashing package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/hashing"}, {"name": "illuminate/macroable", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "aed81891a6e046fdee72edd497f822190f61c162"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/aed81891a6e046fdee72edd497f822190f61c162", "reference": "aed81891a6e046fdee72edd497f822190f61c162", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.3|^8.0"}, "time": "2021-11-16T13:57:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/macroable"}, {"name": "illuminate/mail", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/mail.git", "reference": "557c01a4c6d3862829b004f198c1777a7f8fc35f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/mail/zipball/557c01a4c6d3862829b004f198c1777a7f8fc35f", "reference": "557c01a4c6d3862829b004f198c1777a7f8fc35f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "league/commonmark": "^1.3|^2.0.2", "php": "^7.3|^8.0", "psr/log": "^1.0|^2.0", "swiftmailer/swiftmailer": "^6.3", "tijsverkoyen/css-to-inline-styles": "^2.2.2"}, "suggest": {"aws/aws-sdk-php": "Required to use the SES mail driver (^3.198.1).", "guzzlehttp/guzzle": "Required to use the Mailgun mail driver (^6.5.5|^7.0.1).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "time": "2022-01-05T15:17:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Mail\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Mail package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/mail"}, {"name": "illuminate/pipeline", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "23aeff5b26ae4aee3f370835c76bd0f4e93f71d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/23aeff5b26ae4aee3f370835c76bd0f4e93f71d2", "reference": "23aeff5b26ae4aee3f370835c76bd0f4e93f71d2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "time": "2021-03-26T18:39:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/pipeline"}, {"name": "illuminate/queue", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/queue.git", "reference": "0023daabf67743f7a2bd8328ca2b5537d93e4ae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/queue/zipball/0023daabf67743f7a2bd8328ca2b5537d93e4ae7", "reference": "0023daabf67743f7a2bd8328ca2b5537d93e4ae7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/console": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/database": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/pipeline": "^8.0", "illuminate/support": "^8.0", "laravel/serializable-closure": "^1.0", "opis/closure": "^3.6", "php": "^7.3|^8.0", "ramsey/uuid": "^4.2.2", "symfony/process": "^5.4"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver and DynamoDb failed job storage (^3.198.1).", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "illuminate/redis": "Required to use the Redis queue driver (^8.0).", "pda/pheanstalk": "Required to use the Beanstalk queue driver (^4.0)."}, "time": "2022-07-21T19:36:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Queue\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Queue package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/queue"}, {"name": "illuminate/session", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/session.git", "reference": "9c9988d7229d888c098eebbbb9fcb8c68580411c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/session/zipball/9c9988d7229d888c098eebbbb9fcb8c68580411c", "reference": "9c9988d7229d888c098eebbbb9fcb8c68580411c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0", "symfony/finder": "^5.4", "symfony/http-foundation": "^5.4"}, "suggest": {"illuminate/console": "Required to use the session:table command (^8.0)."}, "time": "2022-01-13T18:28:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Session\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Session package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/session"}, {"name": "illuminate/support", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "1c79242468d3bbd9a0f7477df34f9647dde2a09b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/1c79242468d3bbd9a0f7477df34f9647dde2a09b", "reference": "1c79242468d3bbd9a0f7477df34f9647dde2a09b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "^1.4|^2.0", "ext-json": "*", "ext-mbstring": "*", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "nesbot/carbon": "^2.53.1", "php": "^7.3|^8.0", "voku/portable-ascii": "^1.6.1"}, "conflict": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^8.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^1.3|^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.2.2).", "symfony/process": "Required to use the composer class (^5.4).", "symfony/var-dumper": "Required to use the dd function (^5.4).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "time": "2022-09-21T21:30:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/support"}, {"name": "illuminate/translation", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/translation.git", "reference": "e119d1e55351bd846579c333dd24f9a042b724b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/translation/zipball/e119d1e55351bd846579c333dd24f9a042b724b2", "reference": "e119d1e55351bd846579c333dd24f9a042b724b2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "time": "2022-05-02T13:55:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Translation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/translation"}, {"name": "illuminate/validation", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/validation.git", "reference": "bb104f15545a55664755f58a278c7013f835918a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/validation/zipball/bb104f15545a55664755f58a278c7013f835918a", "reference": "bb104f15545a55664755f58a278c7013f835918a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"egulias/email-validator": "^2.1.10", "ext-json": "*", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "illuminate/translation": "^8.0", "php": "^7.3|^8.0", "symfony/http-foundation": "^5.4", "symfony/mime": "^5.4"}, "suggest": {"ext-bcmath": "Required to use the multiple_of validation rule.", "illuminate/database": "Required to use the database presence verifier (^8.0)."}, "time": "2022-05-30T13:21:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Validation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/validation"}, {"name": "illuminate/view", "version": "v8.83.27", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/view.git", "reference": "5e73eef48d9242532f81fadc14c816a01bfb1388"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/view/zipball/5e73eef48d9242532f81fadc14c816a01bfb1388", "reference": "5e73eef48d9242532f81fadc14c816a01bfb1388", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/events": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "time": "2022-04-14T13:47:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/view"}, {"name": "intervention/image", "version": "2.7.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "time": "2022-05-21T17:30:32+00:00", "type": "library", "extra": {"laravel": {"aliases": {"Image": "Intervention\\Image\\Facades\\Image"}, "providers": ["Intervention\\Image\\ImageServiceProvider"]}, "branch-alias": {"dev-master": "2.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "install-path": "../intervention/image"}, {"name": "jaybizzle/crawler-detect", "version": "v1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "b15237098211b502b9629bbf6f6884a3279420f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/b15237098211b502b9629bbf6f6884a3279420f2", "reference": "b15237098211b502b9629bbf6f6884a3279420f2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "time": "2025-02-06T18:54:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.3.2"}, "install-path": "../jaybizzle/crawler-detect"}, {"name": "jenssegers/agent", "version": "v2.6.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "time": "2020-06-13T08:05:20+00:00", "type": "library", "extra": {"laravel": {"aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}, "providers": ["Jenssegers\\Agent\\AgentServiceProvider"]}, "branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "install-path": "../jenssegers/agent"}, {"name": "laminas/laminas-diactoros", "version": "3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "aca73646e658dce3f079f6b8648c651e193e331e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/aca73646e658dce3f079f6b8648c651e193e331e", "reference": "aca73646e658dce3f079f6b8648c651e193e331e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "psr/http-factory": "^1.0.2", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"psr/http-factory-implementation": "^1.1 || ^2.0", "psr/http-message-implementation": "^1.1 || ^2.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.9.0", "laminas/laminas-coding-standard": "~2.5.0", "php-http/psr7-integration-tests": "^1.3", "phpunit/phpunit": "^9.5.28", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.9"}, "time": "2023-09-03T13:11:37+00:00", "type": "library", "extra": {"laminas": {"module": "Laminas\\Diactoros", "config-provider": "Laminas\\Diactoros\\ConfigProvider"}}, "installation-source": "dist", "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-diactoros"}, {"name": "laminas/laminas-escaper", "version": "2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "ee7a4c37bf3d0e8c03635d5bddb5bb3184ead490"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/ee7a4c37bf3d0e8c03635d5bddb5bb3184ead490", "reference": "ee7a4c37bf3d0e8c03635d5bddb5bb3184ead490", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "conflict": {"zendframework/zend-escaper": "*"}, "require-dev": {"infection/infection": "^0.26.6", "laminas/laminas-coding-standard": "~2.4.0", "maglnet/composer-require-checker": "^3.8.0", "phpunit/phpunit": "^9.5.18", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.22.0"}, "time": "2022-10-10T10:11:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-escaper/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-escaper/issues", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "source": "https://github.com/laminas/laminas-escaper"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-escaper"}, {"name": "laminas/laminas-httphandlerrunner", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laminas/laminas-httphandlerrunner.git", "reference": "7a47834aaad7852816d2ec4fdbb0492163b039ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-httphandlerrunner/zipball/7a47834aaad7852816d2ec4fdbb0492163b039ae", "reference": "7a47834aaad7852816d2ec4fdbb0492163b039ae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0", "psr/http-message": "^1.0", "psr/http-message-implementation": "^1.0", "psr/http-server-handler": "^1.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-diactoros": "^2.18", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.18.0", "vimeo/psalm": "^5.0.0"}, "time": "2023-01-05T21:54:03+00:00", "type": "library", "extra": {"laminas": {"config-provider": "Laminas\\HttpHandlerRunner\\ConfigProvider"}}, "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\HttpHandlerRunner\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Execute PSR-15 RequestHandlerInterface instances and emit responses they generate.", "homepage": "https://laminas.dev", "keywords": ["components", "laminas", "mezzio", "psr-15", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-httphandlerrunner/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-httphandlerrunner/issues", "rss": "https://github.com/laminas/laminas-httphandlerrunner/releases.atom", "source": "https://github.com/laminas/laminas-httphandlerrunner"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-httphandlerrunner"}, {"name": "laminas/laminas-stratigility", "version": "3.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stratigility.git", "reference": "d45eec2f61b9706d9efcb398af53a196c3c7f301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stratigility/zipball/d45eec2f61b9706d9efcb398af53a196c3c7f301", "reference": "d45eec2f61b9706d9efcb398af53a196c3c7f301", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"fig/http-message-util": "^1.1", "laminas/laminas-escaper": "^2.10.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0.2"}, "conflict": {"zendframework/zend-stratigility": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-diactoros": "^2.25 || ^3.0", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.0.0"}, "suggest": {"psr/http-message-implementation": "Please install a psr/http-message-implementation to consume Stratigility; e.g., laminas/laminas-diactoros"}, "time": "2023-05-09T14:23:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions/double-pass-middleware.php", "src/functions/host.php", "src/functions/middleware.php", "src/functions/path.php", "src/functions/double-pass-middleware.legacy.php", "src/functions/host.legacy.php", "src/functions/middleware.legacy.php", "src/functions/path.legacy.php"], "psr-4": {"Laminas\\Stratigility\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR-7 middleware foundation for building and dispatching middleware pipelines", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "middleware", "psr-15", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stratigility/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stratigility/issues", "rss": "https://github.com/laminas/laminas-stratigility/releases.atom", "source": "https://github.com/laminas/laminas-stratigility"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-stratigility"}, {"name": "laravel/serializable-closure", "version": "v1.3.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "4f48ade902b94323ca3be7646db16209ec76be3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/4f48ade902b94323ca3be7646db16209ec76be3d", "reference": "4f48ade902b94323ca3be7646db16209ec76be3d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"illuminate/support": "^8.0|^9.0|^10.0|^11.0", "nesbot/carbon": "^2.61|^3.0", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11|^6.2.0|^7.0.0"}, "time": "2024-11-14T18:34:49+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "install-path": "../laravel/serializable-closure"}, {"name": "league/commonmark", "version": "2.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "d150f911e0079e90ae3c106734c93137c184f932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d150f911e0079e90ae3c106734c93137c184f932", "reference": "d150f911e0079e90ae3c106734c93137c184f932", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.31.1", "commonmark/commonmark.js": "0.31.1", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 | ^7.0", "symfony/process": "^5.4 | ^6.0 | ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 | ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "time": "2024-12-07T15:34:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "install-path": "../league/commonmark"}, {"name": "league/config", "version": "v1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "time": "2022-12-11T20:36:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "install-path": "../league/config"}, {"name": "league/flysystem", "version": "1.1.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "time": "2022-10-04T09:16:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "install-path": "../league/flysystem"}, {"name": "league/mime-type-detection", "version": "1.15.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "time": "2024-01-28T23:22:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/mime-type-detection"}, {"name": "matthi<PERSON><PERSON><PERSON>/minify", "version": "1.3.73", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/matthiasmullie/minify.git", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/minify/zipball/cb7a9297b4ab070909cefade30ee95054d4ae87a", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pcre": "*", "matthiasmullie/path-converter": "~1.1", "php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.0", "matthiasmullie/scrapbook": ">=1.3", "phpunit/phpunit": ">=4.8", "squizlabs/php_codesniffer": ">=3.0"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "time": "2024-03-15T10:27:10+00:00", "bin": ["bin/minifycss", "bin/minifyjs"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.mullie.eu", "role": "Developer"}], "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "homepage": "https://github.com/matthiasmullie/minify", "keywords": ["JS", "css", "javascript", "minifier", "minify"], "support": {"issues": "https://github.com/matthiasmullie/minify/issues", "source": "https://github.com/matthiasmullie/minify/tree/1.3.73"}, "funding": [{"url": "https://github.com/matthiasmullie", "type": "github"}], "install-path": "../matthiasmullie/minify"}, {"name": "mat<PERSON><PERSON><PERSON><PERSON>/path-converter", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/matthiasmullie/path-converter.git", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/path-converter/zipball/e7d13b2c7e2f2268e1424aaed02085518afa02d9", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "time": "2019-02-05T23:41:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MatthiasMullie\\PathConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "Relative path converter", "homepage": "http://github.com/matthiasmullie/path-converter", "keywords": ["converter", "path", "paths", "relative"], "support": {"issues": "https://github.com/matthiasmullie/path-converter/issues", "source": "https://github.com/matthiasmullie/path-converter/tree/1.1.3"}, "install-path": "../matthias<PERSON><PERSON>/path-converter"}, {"name": "middlewares/base-path", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/middlewares/base-path.git", "reference": "243ad6a6435cc74e221d81867d1110853ebf6321"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/middlewares/base-path/zipball/243ad6a6435cc74e221d81867d1110853ebf6321", "reference": "243ad6a6435cc74e221d81867d1110853ebf6321", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0", "psr/http-server-middleware": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3", "laminas/laminas-diactoros": "^2 || ^3", "middlewares/utils": "^2 || ^3 || ^4", "oscarotero/php-cs-fixer-config": "^2", "phpstan/phpstan": "^1 || ^2", "phpunit/phpunit": "^8 || ^9", "squizlabs/php_codesniffer": "^3"}, "time": "2025-03-23T10:15:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Middlewares\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Middleware to remove the prefix from the uri path of the request.", "homepage": "https://github.com/middlewares/base-path", "keywords": ["http", "middleware", "psr-15", "psr-7", "server"], "support": {"issues": "https://github.com/middlewares/base-path/issues", "source": "https://github.com/middlewares/base-path/tree/v2.2.0"}, "install-path": "../middlewares/base-path"}, {"name": "middlewares/base-path-router", "version": "v2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/middlewares/base-path-router.git", "reference": "0f93a817c659084f9d8b70809d24e43b498ac502"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/middlewares/base-path-router/zipball/0f93a817c659084f9d8b70809d24e43b498ac502", "reference": "0f93a817c659084f9d8b70809d24e43b498ac502", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"middlewares/utils": "^2 || ^3 || ^4", "php": "^7.2 || ^8.0", "psr/http-server-middleware": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3", "laminas/laminas-diactoros": "^2 || ^3", "oscarotero/php-cs-fixer-config": "^2", "phpstan/phpstan": "^1 || ^2", "phpunit/phpunit": "^8 || ^9", "squizlabs/php_codesniffer": "^3"}, "time": "2025-03-22T13:12:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Middlewares\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PSR-15 middleware for hierarchical dispatching based on path prefixes", "homepage": "https://github.com/middlewares/base-path-router", "keywords": ["http", "middleware", "psr-15", "psr-7", "routing", "server"], "support": {"issues": "https://github.com/middlewares/base-path-router/issues", "source": "https://github.com/middlewares/base-path-router/tree/v2.1.0"}, "install-path": "../middlewares/base-path-router"}, {"name": "middlewares/request-handler", "version": "v2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/middlewares/request-handler.git", "reference": "732bc798d9ae11c2703b870ccd098030fb609c3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/middlewares/request-handler/zipball/732bc798d9ae11c2703b870ccd098030fb609c3b", "reference": "732bc798d9ae11c2703b870ccd098030fb609c3b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"middlewares/utils": "^2 || ^3 || ^4", "php": "^7.2 || ^8.0", "psr/container": "^1.0||^2.0", "psr/http-server-middleware": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3", "laminas/laminas-diactoros": "^2 || ^3", "oscarotero/php-cs-fixer-config": "^2", "phpstan/phpstan": "^1 || ^2", "phpunit/phpunit": "^8 || ^9", "squizlabs/php_codesniffer": "^3"}, "time": "2025-03-23T18:04:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Middlewares\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Middleware to execute request handlers", "homepage": "https://github.com/middlewares/request-handler", "keywords": ["controller", "handler", "http", "invoke", "middleware", "psr-15", "psr-7", "request", "server"], "support": {"issues": "https://github.com/middlewares/request-handler/issues", "source": "https://github.com/middlewares/request-handler/tree/v2.1.0"}, "install-path": "../middlewares/request-handler"}, {"name": "middlewares/utils", "version": "v3.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/middlewares/utils.git", "reference": "670b135ce0dbd040eadb025a9388f9bd617cc010"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/middlewares/utils/zipball/670b135ce0dbd040eadb025a9388f9bd617cc010", "reference": "670b135ce0dbd040eadb025a9388f9bd617cc010", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0", "psr/container": "^1.0 || ^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "psr/http-server-middleware": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v2.16", "guzzlehttp/psr7": "^2.0", "laminas/laminas-diactoros": "^2.4", "nyholm/psr7": "^1.0", "oscarotero/php-cs-fixer-config": "^1.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8|^9", "slim/psr7": "^1.4", "squizlabs/php_codesniffer": "^3.5", "sunrise/http-message": "^1.0", "sunrise/http-server-request": "^1.0", "sunrise/stream": "^1.0.15", "sunrise/uri": "^1.0.15"}, "time": "2021-07-04T17:56:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Middlewares\\Utils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Common utils for PSR-15 middleware packages", "homepage": "https://github.com/middlewares/utils", "keywords": ["PSR-11", "http", "middleware", "psr-15", "psr-17", "psr-7"], "support": {"issues": "https://github.com/middlewares/utils/issues", "source": "https://github.com/middlewares/utils/tree/v3.3.0"}, "install-path": "../middlewares/utils"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "time": "2023-11-07T21:57:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.45"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "install-path": "../mobiledetect/mobiledetectlib"}, {"name": "monolog/monolog", "version": "1.27.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "time": "2022-06-09T08:53:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "nesbot/carbon", "version": "2.73.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "time": "2025-01-08T20:10:23+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "nette/schema", "version": "v1.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/0462f0166e823aad657c9224d0f849ecac1ba10a", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": "7.1 - 8.3"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.7"}, "time": "2023-10-05T20:37:59+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.5"}, "install-path": "../nette/schema"}, {"name": "nette/utils", "version": "v4.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "time": "2024-01-17T16:50:36+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.4"}, "install-path": "../nette/utils"}, {"name": "nikic/fast-route", "version": "v0.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "31fa86924556b80735f98b294a7ffdfb26789f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/31fa86924556b80735f98b294a7ffdfb26789f22", "reference": "31fa86924556b80735f98b294a7ffdfb26789f22", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "time": "2015-06-18T19:15:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "install-path": "../nikic/fast-route"}, {"name": "opis/closure", "version": "3.6.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/3d81e4309d2a927abbe66df935f4bb60082805ad", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2022-01-27T09:35:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php"], "psr-4": {"Opis\\Closure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.3"}, "install-path": "../opis/closure"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/container", "version": "1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:50:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-04-10T20:10:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/http-server-handler", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-04-10T20:06:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "install-path": "../psr/http-server-handler"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "time": "2023-04-11T06:14:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "install-path": "../psr/http-server-middleware"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "install-path": "../psr/simple-cache"}, {"name": "pusher/pusher-php-server", "version": "2.6.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/pusher/pusher-http-php.git", "reference": "2cf2ba85e7ce3250468a1c42ab7c948a7d43839d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pusher/pusher-http-php/zipball/2cf2ba85e7ce3250468a1c42ab7c948a7d43839d", "reference": "2cf2ba85e7ce3250468a1c42ab7c948a7d43839d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-curl": "*", "php": ">=5.2"}, "require-dev": {"phpunit/phpunit": "~4"}, "time": "2017-06-06T16:41:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Library for interacting with the Pusher REST API", "homepage": "https://github.com/pusher/pusher-php-server", "keywords": ["events", "php-pusher-server", "publish", "pusher", "realtime", "rest", "trigger"], "support": {"issues": "https://github.com/pusher/pusher-http-php/issues", "source": "https://github.com/pusher/pusher-http-php/tree/master"}, "install-path": "../pusher/pusher-php-server"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "ramsey/collection", "version": "1.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "time": "2022-12-27T19:12:24+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "installation-source": "dist", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "install-path": "../ramsey/collection"}, {"name": "ramsey/uuid", "version": "4.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "time": "2025-06-01T06:28:46+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}}, "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.8.1"}, "install-path": "../ramsey/uuid"}, {"name": "s9e/regexp-builder", "version": "1.4.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/s9e/RegexpBuilder.git", "reference": "3a646bc7c40dba41903b7065f32230721e00df3a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/s9e/RegexpBuilder/zipball/3a646bc7c40dba41903b7065f32230721e00df3a", "reference": "3a646bc7c40dba41903b7065f32230721e00df3a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"lib-pcre": ">=7.2", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": ">=9.1"}, "time": "2022-03-05T16:22:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"s9e\\RegexpBuilder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Single-purpose library that generates regular expressions that match a list of strings.", "homepage": "https://github.com/s9e/RegexpBuilder/", "keywords": ["regexp"], "support": {"issues": "https://github.com/s9e/RegexpBuilder/issues", "source": "https://github.com/s9e/RegexpBuilder/tree/1.4.6"}, "install-path": "../s9e/regexp-builder"}, {"name": "s9e/sweetdom", "version": "2.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/s9e/SweetDOM.git", "reference": "482d42537a1e0ab98924a9d932b724a175302fe8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/s9e/SweetDOM/zipball/482d42537a1e0ab98924a9d932b724a175302fe8", "reference": "482d42537a1e0ab98924a9d932b724a175302fe8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "php": ">=8.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-10-07T13:06:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"s9e\\SweetDOM\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Syntactic sugar for the DOM API with a focus on XSLT 1.0 template manipulation.", "homepage": "https://github.com/s9e/SweetDOM/", "keywords": ["dom", "xsl", "xslt"], "support": {"issues": "https://github.com/s9e/SweetDOM/issues", "source": "https://github.com/s9e/SweetDOM/tree/2.1.2"}, "install-path": "../s9e/sweetdom"}, {"name": "s9e/text-formatter", "version": "2.14.3", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/s9e/TextFormatter.git", "reference": "bec02b24b78e4bc292c731b334d7cd31be72c3c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/s9e/TextFormatter/zipball/bec02b24b78e4bc292c731b334d7cd31be72c3c7", "reference": "bec02b24b78e4bc292c731b334d7cd31be72c3c7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-filter": "*", "lib-pcre": ">=8.13", "php": "^8.0", "s9e/regexp-builder": "^1.4", "s9e/sweetdom": "^2.0"}, "require-dev": {"code-lts/doctum": "*", "matthiasmullie/minify": "*", "phpunit/phpunit": "^9.5"}, "suggest": {"ext-curl": "Improves the performance of the MediaEmbed plugin and some JavaScript minifiers", "ext-intl": "Allows international URLs to be accepted by the URL filter", "ext-json": "Enables the generation of a JavaScript parser", "ext-mbstring": "Improves the performance of the PHP renderer", "ext-tokenizer": "Improves the performance of the PHP renderer", "ext-xsl": "Enables the XSLT renderer", "ext-zlib": "Enables gzip compression when scraping content via the MediaEmbed plugin"}, "time": "2023-11-11T15:27:53+00:00", "type": "library", "extra": {"version": "2.14.3"}, "installation-source": "dist", "autoload": {"psr-4": {"s9e\\TextFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Multi-purpose text formatting and markup library. Plugins offer support for BBCodes, Markdown, emoticons, HTML, embedding third-party media (YouTube, etc...), enhanced typography and more.", "homepage": "https://github.com/s9e/TextFormatter/", "keywords": ["bbcode", "bbcodes", "blog", "censor", "embed", "emoji", "emoticons", "engine", "forum", "html", "markdown", "markup", "media", "parser", "shortcodes"], "support": {"issues": "https://github.com/s9e/TextFormatter/issues", "source": "https://github.com/s9e/TextFormatter/tree/2.14.3"}, "install-path": "../s9e/text-formatter"}, {"name": "staudenmeir/eloquent-eager-limit", "version": "v1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/staudenmeir/eloquent-eager-limit.git", "reference": "439135c4b3361a313c2e7102d68bf807518d1bf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/staudenmeir/eloquent-eager-limit/zipball/439135c4b3361a313c2e7102d68bf807518d1bf9", "reference": "439135c4b3361a313c2e7102d68bf807518d1bf9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/database": "^8.0", "php": "^7.3|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-11-22T18:13:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Staudenmeir\\EloquentEagerLimit\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Eloquent eager loading with limit", "support": {"issues": "https://github.com/staudenmeir/eloquent-eager-limit/issues", "source": "https://github.com/staudenmeir/eloquent-eager-limit/tree/v1.6.1"}, "funding": [{"url": "https://paypal.me/JonasS<PERSON>nmeir", "type": "custom"}], "install-path": "../staudenmeir/eloquent-eager-limit"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "time": "2021-10-18T15:26:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "installation-source": "dist", "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "install-path": "../swiftmailer/swiftmailer"}, {"name": "sycho/codecs-base64vlq", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/SychO9/codecs-base64vlq.git", "reference": "210932edfb29049831e4def7f11a264944132ac9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SychO9/codecs-base64vlq/zipball/210932edfb29049831e4def7f11a264944132ac9", "reference": "210932edfb29049831e4def7f11a264944132ac9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "sycho/errors": "^3.0"}, "require-dev": {"sycho/errors": "^3.x-dev"}, "time": "2022-04-09T13:12:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"axy\\codecs\\base64vlq\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Codec for VLQ (variable-length quantity) Base64 algorithm", "homepage": "https://github.com/sycho9/codecs-base64vlq", "keywords": ["Source map", "VLQ", "Variable length quantity", "base64", "codec"], "support": {"source": "https://github.com/SychO9/codecs-base64vlq/tree/2.0.0"}, "install-path": "../sycho/codecs-base64vlq"}, {"name": "sycho/errors", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/SychO9/errors.git", "reference": "82e955d247940aa7feed35e1ec7a61fc46639582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SychO9/errors/zipball/82e955d247940aa7feed35e1ec7a61fc46639582", "reference": "82e955d247940aa7feed35e1ec7a61fc46639582", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"axy/backtrace": "~2.0.0", "php": ">=7.3"}, "require-dev": {"phpmd/phpmd": "^2.6", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2022-04-09T13:10:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"axy\\errors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Exceptions in PHP", "homepage": "https://github.com/sycho9/errors", "keywords": ["error", "exception"], "support": {"source": "https://github.com/SychO9/errors/tree/3.0.0"}, "install-path": "../sycho/errors"}, {"name": "sycho/json-api", "version": "v0.5.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/SychO9/json-api-php.git", "reference": "5ef867317a6b39b307af0fc98c5b9c5828607301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SychO9/json-api-php/zipball/5ef867317a6b39b307af0fc98c5b9c5828607301", "reference": "5ef867317a6b39b307af0fc98c5b9c5828607301", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "time": "2023-04-16T10:46:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "0.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Tobscure\\JsonApi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "JSON-API responses in PHP", "keywords": ["api", "json", "jsonapi", "standard"], "support": {"source": "https://github.com/SychO9/json-api-php/tree/v0.5.2"}, "install-path": "../sycho/json-api"}, {"name": "sycho/sourcemap", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/SychO9/sourcemap.git", "reference": "81d514186e37efbea7f4dd701ea9133fd3412bf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SychO9/sourcemap/zipball/81d514186e37efbea7f4dd701ea9133fd3412bf1", "reference": "81d514186e37efbea7f4dd701ea9133fd3412bf1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.3", "sycho/codecs-base64vlq": "^2.0", "sycho/errors": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "sycho/codecs-base64vlq": "^2.x-dev", "sycho/errors": "^3.x-dev"}, "time": "2022-04-09T13:13:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"axy\\sourcemap\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Work with JavaScript/CSS Source Map", "homepage": "https://github.com/sycho9/sourcemap", "keywords": ["Source map", "css", "javascript", "sourcemap"], "support": {"source": "https://github.com/SychO9/sourcemap/tree/2.0.0"}, "install-path": "../sycho/sourcemap"}, {"name": "symfony/config", "version": "v5.4.46", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "977c88a02d7d3f16904a81907531b19666a08e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/977c88a02d7d3f16904a81907531b19666a08e78", "reference": "977c88a02d7d3f16904a81907531b19666a08e78", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "time": "2024-10-30T07:58:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/config"}, {"name": "symfony/console", "version": "v5.4.47", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "time": "2024-11-06T11:30:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/css-selector", "version": "v6.0.19", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f1d00bddb83a4cb2138564b2150001cb6ce272b1", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2"}, "time": "2023-01-01T08:36:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/css-selector"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2"}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/72982eb416f61003e9bb6e91f8b3213600dcf9e9", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/filesystem", "version": "v6.0.19", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "3d49eec03fda1f0fc19b7349fbbe55ebc1004214"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/3d49eec03fda1f0fc19b7349fbbe55ebc1004214", "reference": "3d49eec03fda1f0fc19b7349fbbe55ebc1004214", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "time": "2023-01-20T17:44:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "63741784cd7b9967975eec610b256eed3ede022b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/63741784cd7b9967975eec610b256eed3ede022b", "reference": "63741784cd7b9967975eec610b256eed3ede022b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "time": "2024-09-28T13:32:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-foundation", "version": "v5.4.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/3f38b8af283b830e1363acd79e5bc3412d055341", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "time": "2024-11-13T18:58:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/mime", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/8c1b9b3e5b52981551fc6044539af1d974e39064", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "time": "2024-10-23T20:18:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/mime"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-iconv"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-messageformatter", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-messageformatter.git", "reference": "d2ed9f44f1ccb21d36e51ebb1f7b1ef26e36e0de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-messageformatter/zipball/d2ed9f44f1ccb21d36e51ebb1f7b1ef26e36e0de", "reference": "d2ed9f44f1ccb21d36e51ebb1f7b1ef26e36e0de", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\MessageFormatter\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's MessageFormatter class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "messageformatter", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-messageformatter/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-messageformatter"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php81"}, {"name": "symfony/process", "version": "v5.4.47", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5d1662fb32ebc94f17ddb8d635454a776066733d", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "time": "2024-11-06T11:36:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/service-contracts", "version": "v2.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f37b419f7aea2e9abf10abd261832cace12e3300", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/string", "version": "v6.0.19", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/d9e72497367c23e08bf94176d2be45b00a9d232a", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/translation-contracts": "^2.0|^3.0", "symfony/var-exporter": "^5.4|^6.0"}, "time": "2023-01-01T08:36:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "symfony/translation", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/98f26acc99341ca4bab345fb14d7b1d7cb825bed", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v2.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "reference": "450d4172653f38818657022252f9d81be89ee9a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "time": "2024-09-25T14:11:13+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "symfony/yaml", "version": "v5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "time": "2024-09-25T14:11:13+00:00", "bin": ["Resources/bin/yaml-lint"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/yaml"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d", "reference": "0d72ac1c00084279c1816675284073c5a337c20d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.4 || ^8.0", "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5.10"}, "time": "2024-12-21T16:25:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"}, "install-path": "../tijsverkoyen/css-to-inline-styles"}, {"name": "voku/portable-ascii", "version": "1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/87337c91b9dfacee02452244ee14ab3c43bc485a", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2022-01-24T18:55:24+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/1.6.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "install-path": "../voku/portable-ascii"}, {"name": "webmozart/assert", "version": "1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "time": "2022-06-03T18:03:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "install-path": "../webmozart/assert"}, {"name": "wikimedia/less.php", "version": "v3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wikimedia/less.php.git", "reference": "0d5b30ba792bdbf8991a646fc9c30561b38a5559"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wikimedia/less.php/zipball/0d5b30ba792bdbf8991a646fc9c30561b38a5559", "reference": "0d5b30ba792bdbf8991a646fc9c30561b38a5559", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.9"}, "require-dev": {"mediawiki/mediawiki-codesniffer": "40.0.1", "mediawiki/mediawiki-phan-config": "0.12.0", "mediawiki/minus-x": "1.1.1", "php-parallel-lint/php-console-highlighter": "1.0.0", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpunit/phpunit": "^8.5"}, "time": "2023-02-03T06:43:41+00:00", "bin": ["bin/lessc"], "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Less": "lib/"}, "classmap": ["lessc.inc.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://timotijhof.net"}, {"name": "<PERSON>", "homepage": "https://github.com/oyejorge"}, {"name": "<PERSON>", "homepage": "https://github.com/agar"}, {"name": "<PERSON>", "homepage": "https://github.com/Mordred"}], "description": "PHP port of the LESS processor", "homepage": "https://gerrit.wikimedia.org/g/mediawiki/libs/less.php", "keywords": ["css", "less", "less.js", "lesscss", "php", "stylesheet"], "support": {"issues": "https://github.com/wikimedia/less.php/issues", "source": "https://github.com/wikimedia/less.php/tree/v3.2.1"}, "install-path": "../wikimedia/less.php"}], "dev": true, "dev-package-names": []}
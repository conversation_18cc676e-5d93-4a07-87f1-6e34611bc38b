<?php

/*
 * This file is part of Enterprise Points.
 *
 * For detailed copyright and license information, please view the
 * LICENSE file that was distributed with this source code.
 */

namespace Enterprise\Points\Api\Controller;

use Enterprise\Points\Api\Serializer\UserPointPurchaseSerializer;
use Enterprise\Points\Model\PointStoreItem;
use Enterprise\Points\Model\UserPointPurchase;
use Enterprise\Points\Service\PointsService;
use Flarum\Api\Controller\AbstractCreateController;
use Flarum\Http\RequestUtil;
use Flarum\User\Exception\PermissionDeniedException;
use Illuminate\Support\Arr;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

/**
 * Controller for creating store purchases.
 */
class CreatePurchaseController extends AbstractCreateController
{
    /**
     * {@inheritdoc}
     */
    public $serializer = UserPointPurchaseSerializer::class;

    /**
     * {@inheritdoc}
     */
    public $include = ['user', 'storeItem'];

    /**
     * @var PointsService
     */
    protected $pointsService;

    /**
     * @param PointsService $pointsService
     */
    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * {@inheritdoc}
     */
    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $attributes = Arr::get($request->getParsedBody(), 'data.attributes', []);

        $storeItemId = (int) Arr::get($attributes, 'storeItemId');
        $quantity = (int) Arr::get($attributes, 'quantity', 1);

        // Validate required fields
        if (!$storeItemId || $quantity <= 0) {
            throw new \InvalidArgumentException('Missing required fields: storeItemId, quantity');
        }

        // Check if store is enabled
        if (!app('flarum.settings')->get('enterprise-points.enable_point_store', true)) {
            throw new \Exception('Point store is disabled');
        }

        // Get store item
        $storeItem = PointStoreItem::findOrFail($storeItemId);

        // Check if item is active
        if (!$storeItem->is_active) {
            throw new \Exception('This item is not available for purchase');
        }

        // Check stock
        if (!$storeItem->isInStock()) {
            throw new \Exception('This item is out of stock');
        }

        if (!$storeItem->hasUnlimitedStock() && $storeItem->stock < $quantity) {
            throw new \Exception('Insufficient stock available');
        }

        // Check purchase limit
        if ($storeItem->hasPurchaseLimit()) {
            $userPurchases = UserPointPurchase::where('user_id', $actor->id)
                ->where('store_item_id', $storeItemId)
                ->sum('quantity');

            if ($userPurchases + $quantity > $storeItem->purchase_limit) {
                throw new \Exception('Purchase limit exceeded');
            }
        }

        // Calculate total cost
        $totalCost = $storeItem->cost * $quantity;

        // Check if user has enough points
        if (($actor->points ?? 0) < $totalCost) {
            throw new \Exception('Insufficient points');
        }

        // Process the purchase
        return app('db')->transaction(function () use ($actor, $storeItem, $quantity, $totalCost) {
            // Spend points
            $this->pointsService->spendPoints(
                $actor,
                $totalCost,
                'store_purchase',
                "Purchased {$quantity}x {$storeItem->name}",
                [
                    'store_item_id' => $storeItem->id,
                    'quantity' => $quantity,
                    'unit_cost' => $storeItem->cost
                ]
            );

            // Create purchase record
            $purchase = UserPointPurchase::create([
                'user_id' => $actor->id,
                'store_item_id' => $storeItem->id,
                'cost_paid' => $storeItem->cost,
                'quantity' => $quantity,
                'item_snapshot' => [
                    'name' => $storeItem->name,
                    'description' => $storeItem->description,
                    'type' => $storeItem->type,
                    'icon' => $storeItem->icon,
                    'color' => $storeItem->color,
                    'metadata' => $storeItem->metadata
                ],
                'is_active' => true,
                'expires_at' => $this->calculateExpiryDate($storeItem)
            ]);

            // Update stock
            $storeItem->decreaseStock($quantity);

            return $purchase;
        });
    }

    /**
     * Calculate expiry date for the item based on its metadata.
     *
     * @param PointStoreItem $item
     * @return \Carbon\Carbon|null
     */
    protected function calculateExpiryDate(PointStoreItem $item): ?\Carbon\Carbon
    {
        $duration = $item->getMetadata('duration_days');
        
        if ($duration && is_numeric($duration)) {
            return now()->addDays((int) $duration);
        }

        return null;
    }
}
